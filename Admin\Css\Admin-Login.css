:root{
  --primary-color: #edf6ff;
  /* #C4E0FE */
  --secondary-color: #036ce2;
  --tertiary-color: #C5E0FE;
  --primary-font: 'Poppins', sans-serif;

}

body {
  overflow:scroll;
  background-color: var(--primary-color);
  /* border: 1em solid red; */
  font-family: var(--primary-font);
}
html{
  font-size: 14px;
}
.auth {
  position:absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
  /* border: 3px solid black; */
  width: 70%;
  max-width: 1000px;
  height: fit-content;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  overflow: hidden;
  border-radius: 20px;
  box-shadow: 0 0 10px -2px rgba(47, 47, 47, 0.357);
  background-color: white;
}

.login-image {
  display: flexbox;
  width: calc(50% - 10px);
  height: 400px;
  background-image: url(../Public/Images/login.jpg);
  background-repeat: no-repeat;
  background-size:135%;
  background-position: center;
  border-bottom: 2px solid var(--secondary-color);
  /* outline: 2px solid black; */
}

.login-card {
  width: calc(50% - 10px);
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* justify-content: center; */
  /* border: 2px solid black; */
  /* gap:2rem; */

}

.login-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0rem;
  width: 100%;
  /* border: 2px solid black; */
  font-size: 3rem;
  /* color: var(--secondary-color); */
  /* font-family: var(--primary-font); */
  /* margin-bottom: 1rem; */
  margin: 2rem 0 0rem 0;
}
.login-icon > img{
  width: 4rem;

}
.login-icon>h1 {
  font-size: 2.3rem;
  /* margin-top: 0.5rem; */
  padding: 0;
  font-weight: 600;
  color: black;
  text-transform: capitalize;
  /* border: 2px solid black; */
}

.login-form {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: .7rem;
  /* border: 2px solid black; */
  margin:.5rem 0 1.5rem 0;
  width: 100%;
  font-family: var(--primary-font);
  /* font-size: 17px; */
}
.login-form>.error-msg{
  position: relative;
  /* left:-3rem; */
  width: 80%;
  text-align: left;
  color: red;
  /* display: none; */
  background-color: rgba(255, 0, 0, 0.184);
  padding: 5px 15px;
  border-left: 3px solid red;
  font-size: 1rem;
  font-weight: 600;
  text-transform: capitalize;
  /* border: 2px solid black; */
  margin: .5rem 0 .5rem 0;
}

.login-form>.success-msg{
  position: relative;
  width: 80%;
  text-align: left;
  color: #27ae60;
  background-color: rgba(39, 174, 96, 0.184);
  padding: 5px 15px;
  border-left: 3px solid #27ae60;
  font-size: 1rem;
  font-weight: 600;
  text-transform: capitalize;
  margin: .5rem 0 .5rem 0;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-form>.input-group {
  position: relative;
  width: 80%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: .4rem;
  /* margin-bottom: 1rem; */
  /* border: 2px solid red; */
}

.login-form>.input-group>.login-labels {
  font-size: 1rem;
  color: black;
  font-weight: 550;
  font-style: bold;
  text-transform: capitalize;
}

.login-form>.input-group>i,.login-form>.input-group>.eye{
  position: absolute;
  transition: all .3s linear;
  /* transform: scale(0); */
  top:calc(50% + .4rem);
  cursor: pointer;
  /* border: 2px solid black; */
  padding: 1px 0px 0px 0px;
  right:0;
background-color: white;
  font-size: 1.2rem;
  color: black;
  margin-right: 10px;
}
.login-form>.input-group>.eye{
  width: 1.4rem;
  top:calc(50% + .2rem);
}
.login-form>.input-group>.cross{
transform: scale(0);
}
.login-form>.input-group>.fa-eye-slash{
transform: scale(0);
}

.login-form>.input-group>.login-fields {
  height: 3rem;
  border-radius: .6rem;
  border: 2px solid rgba(98, 98, 98, 0.197);
  /* box-shadow: 0 0 3px rgba(47, 47, 47, 0.357); */
  outline: none;
  padding: 0 1rem;
  font-size: 1.1rem;
  color: black;
  font-weight: 500;
  font-family: var(--primary-font);

}

.login-form>.input-group>.login-fields:focus {
  border: 2px solid var(--secondary-color);
  /* box-shadow: 0 0 3px var(--secondary-color); */
}
.login-form>.input-group>.login-fields::placeholder {
  color: rgba(98, 98, 98, 0.504);
  font-weight: 500;
  font-size: 1rem;
  font-family: var(--primary-font);
}

.login-form>.input-group>.login-fields:user-invalid{
  animation: shake .6s linear;
  border: 2px solid red;
  transition: all .5s linear;
}

.login-form>input[type="submit"] {
  width: 80%;
  height: 3rem;
  font-size: 1.2rem;
  cursor: pointer;
  background-color: #0077ff;

  border: 2px solid var(--secondary-color);
  color:white;
  border: none;
  border-radius: .6rem;
  /* box-shadow: 0 0 3px rgba(47, 47, 47, 0.357); */
  outline: none;
  font-weight: 600;
  font-family: var(--primary-font);
  transition: all .4s ease;
}
.login-form>input[type="submit"]:hover{
  background-color: var(--secondary-color);
  transform: scale(1.02);
}
.login-card>.login-form>.forgot-password{
  position:relative;
  color: var(--secondary-color);
  font-size: .9rem;
  /* font-weight: 600; */
  text-decoration: none;
  /* margin-bottom: 1rem; */
}

@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  50%,
  90% {
    transform: translateX(-10px);
  }

  30%,
  70% {
    transform: translateX(10px);
  }
}

@media (max-width: 720px) {
  html{
    font-size: 12px;
  }
  .auth {
    width: 95%;
    height: fit-content;
  }
}
@media (min-width:250px) and (max-width: 460px){
  html{
    font-size: 14px;
  }
  body{
    height:120vh;
  }
  .auth{
    width: 90%;
  }
  .login-image{
    display: none;

  }
  .login-card{
    width: 100%;
  }
}
@media (max-width:250px){
  html{
    font-size: 8px;
  }
  .auth{
    width: 90%;
  }
  .login-image{
    display: none;

  }
  .login-card{
    width: 100%;
  }
}