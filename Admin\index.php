<?php
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);
// Simple routing with a switch statement
switch ($path) {
    case '/SSASIT/Admin/Dashboard':
        // Handle the root path
        require 'Pages/Admin-Dashboard.php';
        break;
    case '/SSASIT/Admin/Authentication':
        // Handle the '/about' path
        require 'Pages/Authentication.php';
        break;
    default:
        // Handle 404 Not Found errors for all other paths
        http_response_code(404);
        require 'Pages/404.html';
        break;
}
?>