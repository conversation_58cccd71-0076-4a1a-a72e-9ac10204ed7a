/* Student Add Form CSS - Beginner Friendly */

/* CSS Root Variables */
:root {
    /* Color Variables */
    --primary-color: #edf6ff;
    --secondary-color: #036ce2;
    --tertiary-color: #C5E0FE;
    --primary-font: 'Poppins', sans-serif;
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --dark-gray: #2c3e50;
    --text-color: #333333;
    --border-color: #e1e5e9;
    --success-color: #27ae60;
    --error-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #3498db;

    /* Background Colors */
    --bg-gradient: linear-gradient(135deg, #edf6ff 0%, #c3cfe2 100%);
    --header-gradient: linear-gradient(135deg, #036ce2, #0077ff);
    --button-gradient: linear-gradient(135deg, #036ce2, #0077ff);
    --button-hover-gradient: linear-gradient(135deg, #0077ff, #005bb5);

    /* Spacing Variables */
    --border-radius: 1rem;
    --border-radius-large: 20px;
    --padding-small: 8px;
    --padding-medium: 12px;
    --padding-large: 15px;
    --padding-xl: 20px;
    --padding-xxl: 30px;
    --padding-xxxl: 40px;
    --margin-small: 5px;
    --margin-medium: 10px;
    --margin-large: 15px;
    --margin-xl: 20px;
    --margin-xxl: 25px;
    --margin-xxxl: 40px;

    /* Font Sizes */
    --font-size-small: 0.85rem;
    --font-size-normal: 1rem;
    --font-size-medium: 1.1rem;
    --font-size-large: 1.2rem;
    --font-size-xl: 1.5rem;
    --font-size-xxl: 2rem;
    --font-size-xxxl: 2.5rem;

    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Shadows */
    --shadow-small: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-large: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-button: 0 4px 15px rgba(3, 108, 226, 0.3);
    --shadow-button-hover: 0 6px 20px rgba(3, 108, 226, 0.4);
    --shadow-focus: 0 0 0 3px rgba(3, 108, 226, 0.1);

    /* Transitions */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;

    /* Animation Durations */
    --animation-fast: 0.3s;
    --animation-normal: 0.6s;
    --animation-slow: 0.8s;
}

/* Basic Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
ht

/* Main Styles */
body {
    font-family: var(--primary-font);
    background: var(--bg-gradient);
    min-height: 100vh;
    padding: var(--padding-xl);
}

/* Container */
.container {
    max-width: 900px;
    margin: 0 auto;
    background: var(--white);
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-large);
    overflow: hidden;
    animation: slideUp var(--animation-normal) ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Header */
.form-header {
    background: var(--header-gradient);
    color: var(--white);
    padding: var(--padding-xxl);
    text-align: center;
}

.form-header h1 {
    font-size: var(--font-size-xxxl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--margin-medium);
    animation: fadeIn var(--animation-slow) ease-out 0.2s both;
}

.form-header p {
    font-size: var(--font-size-medium);
    opacity: 0.9;
    animation: fadeIn var(--animation-slow) ease-out 0.4s both;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Styles */
.student-form {
    padding: var(--padding-xxxl);
}

/* Form Sections */
.form-section {
    margin-bottom: var(--margin-xxxl);
    animation: fadeInUp var(--animation-normal) ease-out;
}

.section-title {
    color: var(--secondary-color);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--margin-xxl);
    padding-bottom: var(--padding-medium);
    border-bottom: 2px solid var(--primary-color);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: var(--secondary-color);
    animation: expandWidth var(--animation-slow) ease-out;
}

@keyframes expandWidth {
    from { width: 0; }
    to { width: 50px; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Rows and Groups */
.form-row {
    display: flex;
    gap: var(--padding-xl);
    margin-bottom: var(--margin-xl);
}

.form-group {
    flex: 1;
    position: relative;
}

.form-group.full-width {
    flex: 100%;
}

/* Labels */
label {
    display: block;
    color: var(--text-color);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--margin-small);
    font-size: 0.95rem;
}

/* Input Styles */
input, select, textarea {
    width: 100%;
    padding: var(--padding-medium) var(--padding-large);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-normal);
    font-family: var(--primary-font);
    background: var(--light-gray);
    transition: var(--transition-normal);
    outline: none;
}

input:focus, select:focus, textarea:focus {
    border-color: var(--secondary-color);
    background: var(--white);
    box-shadow: var(--shadow-focus);
    transform: translateY(-2px);
}

input:valid, select:valid, textarea:valid {
    border-color: var(--success-color);
}

/* Textarea */
textarea {
    resize: vertical;
    min-height: 80px;
    max-height: 150px;
}

/* File Input */
input[type="file"] {
    padding: var(--padding-small);
    background: var(--white);
    border: 2px dashed var(--secondary-color);
    cursor: pointer;
}

input[type="file"]:hover {
    background: var(--primary-color);
}

/* Select Dropdown */
select {
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

/* Error Styles */
.error {
    border-color: var(--error-color) !important;
    background: #fff5f5 !important;
    animation: shake var(--animation-fast) ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.error-message {
    color: var(--error-color);
    font-size: var(--font-size-small);
    margin-top: var(--margin-small);
    display: none;
    font-weight: var(--font-weight-medium);
}

/* Buttons */
.form-buttons {
    display: flex;
    gap: var(--margin-large);
    justify-content: center;
    margin-top: var(--margin-xxxl);
    padding-top: var(--padding-xxl);
    border-top: 1px solid #eee;
}

.btn {
    padding: var(--padding-medium) var(--padding-xxl);
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-size-normal);
    font-weight: var(--font-weight-semibold);
    font-family: var(--primary-font);
    cursor: pointer;
    transition: var(--transition-normal);
    min-width: 150px;
}

.btn-primary {
    background: var(--button-gradient);
    color: var(--white);
    box-shadow: var(--shadow-button);
}

.btn-primary:hover {
    background: var(--button-hover-gradient);
    transform: translateY(-2px);
    box-shadow: var(--shadow-button-hover);
}

.btn-secondary {
    background: var(--light-gray);
    color: #666;
    border: 2px solid var(--border-color);
}

.btn-secondary:hover {
    background: #e9ecef;
    border-color: var(--secondary-color);
    color: var(--secondary-color);
    transform: translateY(-2px);
}

.btn:active {
    transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: var(--margin-medium);
        border-radius: var(--margin-large);
    }

    .form-header {
        padding: var(--padding-xl);
    }

    .form-header h1 {
        font-size: var(--font-size-xxl);
    }

    .student-form {
        padding: var(--padding-xl);
    }

    .form-row {
        flex-direction: column;
        gap: var(--margin-large);
    }

    .form-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 250px;
    }
}

@media (max-width: 480px) {
    body {
        padding: var(--padding-medium);
    }

    .form-header h1 {
        font-size: 1.8rem;
    }

    .student-form {
        padding: var(--padding-large);
    }

    input, select, textarea {
        padding: var(--padding-medium) var(--padding-medium);
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Loading Animation */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading .btn-primary::after {
    content: '';
    width: 16px;
    height: 16px;
    margin-left: 10px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    display: inline-block;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success Animation */
.success {
    border-color: var(--success-color) !important;
    background: #f0fff4 !important;
}

/* Hover Effects */
.form-group:hover input,
.form-group:hover select,
.form-group:hover textarea {
    border-color: var(--secondary-color);
}

/* Focus Within Effect */
.form-group:focus-within label {
    color: var(--secondary-color);
    transform: translateY(-2px);
    transition: var(--transition-normal);
}