/* Student Add Form CSS - Beginner Friendly */

/* Basic Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Main Styles */
body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #edf6ff 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 20px;
}

/* Container */
.container {
    max-width: 900px;
    margin: 0 auto;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Header */
.form-header {
    background: linear-gradient(135deg, #036ce2, #0077ff);
    color: white;
    padding: 30px;
    text-align: center;
}

.form-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    animation: fadeIn 0.8s ease-out 0.2s both;
}

.form-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    animation: fadeIn 0.8s ease-out 0.4s both;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Styles */
.student-form {
    padding: 40px;
}

/* Form Sections */
.form-section {
    margin-bottom: 40px;
    animation: fadeInUp 0.6s ease-out;
}

.section-title {
    color: #036ce2;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 2px solid #edf6ff;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: #036ce2;
    animation: expandWidth 0.8s ease-out;
}

@keyframes expandWidth {
    from { width: 0; }
    to { width: 50px; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Rows and Groups */
.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    flex: 1;
    position: relative;
}

.form-group.full-width {
    flex: 100%;
}

/* Labels */
label {
    display: block;
    color: #333;
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

/* Input Styles */
input, select, textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    font-family: 'Poppins', sans-serif;
    background: #fafafa;
    transition: all 0.3s ease;
    outline: none;
}

input:focus, select:focus, textarea:focus {
    border-color: #036ce2;
    background: white;
    box-shadow: 0 0 0 3px rgba(3, 108, 226, 0.1);
    transform: translateY(-2px);
}

input:valid, select:valid, textarea:valid {
    border-color: #27ae60;
}

/* Textarea */
textarea {
    resize: vertical;
    min-height: 80px;
    max-height: 150px;
}

/* File Input */
input[type="file"] {
    padding: 8px;
    background: white;
    border: 2px dashed #036ce2;
    cursor: pointer;
}

input[type="file"]:hover {
    background: #edf6ff;
}

/* Select Dropdown */
select {
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

/* Error Styles */
.error {
    border-color: #e74c3c !important;
    background: #fff5f5 !important;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.error-message {
    color: #e74c3c;
    font-size: 0.85rem;
    margin-top: 5px;
    display: none;
    font-weight: 500;
}

/* Buttons */
.form-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid #eee;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
}

.btn-primary {
    background: linear-gradient(135deg, #036ce2, #0077ff);
    color: white;
    box-shadow: 0 4px 15px rgba(3, 108, 226, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0077ff, #005bb5);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(3, 108, 226, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 2px solid #e1e5e9;
}

.btn-secondary:hover {
    background: #e9ecef;
    border-color: #036ce2;
    color: #036ce2;
    transform: translateY(-2px);
}

.btn:active {
    transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        border-radius: 15px;
    }

    .form-header {
        padding: 20px;
    }

    .form-header h1 {
        font-size: 2rem;
    }

    .student-form {
        padding: 20px;
    }

    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .form-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 250px;
    }
}

@media (max-width: 480px) {
    body {
        padding: 10px;
    }

    .form-header h1 {
        font-size: 1.8rem;
    }

    .student-form {
        padding: 15px;
    }

    input, select, textarea {
        padding: 10px 12px;
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Loading Animation */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading .btn-primary::after {
    content: '';
    width: 16px;
    height: 16px;
    margin-left: 10px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    display: inline-block;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success Animation */
.success {
    border-color: #27ae60 !important;
    background: #f0fff4 !important;
}

/* Hover Effects */
.form-group:hover input,
.form-group:hover select,
.form-group:hover textarea {
    border-color: #036ce2;
}

/* Focus Within Effect */
.form-group:focus-within label {
    color: #036ce2;
    transform: translateY(-2px);
    transition: all 0.3s ease;
}