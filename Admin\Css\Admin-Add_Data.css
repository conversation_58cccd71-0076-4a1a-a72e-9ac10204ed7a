/* Student Add Form CSS - Beginner Friendly */

/* CSS Root Variables - Simplified */
:root {
    /* Colors */
    --primary-color: #edf6ff;
    --secondary-color: #036ce2;
    --white: #ffffff;
    --text-color: #333333;
    --border-color: #e1e5e9;
    --success-color: #27ae60;
    --error-color: #e74c3c;

    /* Font */
    --primary-font: 'Poppins', sans-serif;

    /* Gradients */
    --bg-gradient: linear-gradient(135deg, #edf6ff 0%, #c3cfe2 100%);
    --header-gradient: linear-gradient(135deg, #036ce2, #0077ff);
    --button-gradient: linear-gradient(135deg, #036ce2, #0077ff);

    /* Shadows */
    --shadow: 0 0.625rem 1.875rem rgba(0, 0, 0, 0.1);
    --shadow-focus: 0 0 0 0.1875rem rgba(3, 108, 226, 0.1);

    /* Transition */
    --transition: all 0.3s ease;
}

/* Basic Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
html{
    font-size: 14px;
}  

/* Main Styles */
body {
    font-family: var(--primary-font);
    background: var(--bg-gradient);
    min-height: 100vh;
    padding: 1.25rem;
}

/* Container */
.container {
    max-width: 900px;
    margin: 0 auto;
    background: var(--white);
    border-radius: 1.25rem;
    box-shadow: var(--shadow);
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Header */
.form-header {
    background: var(--header-gradient);
    color: var(--white);
    padding: 1.875rem;
    text-align: center;
}

.form-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.625rem;
    animation: fadeIn 0.8s ease-out 0.2s both;
}

.form-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    animation: fadeIn 0.8s ease-out 0.4s both;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Styles */
.student-form {
    padding: 2.5rem;
}

/* Form Sections */
.form-section {
    margin-bottom: 2.5rem;
    animation: fadeInUp 0.6s ease-out;
}

.section-title {
    color: var(--secondary-color);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5625rem;
    padding-bottom: 0.75rem;
    border-bottom: 0.125rem solid var(--primary-color);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -0.125rem;
    left: 0;
    width: 3.125rem;
    height: 0.125rem;
    background: var(--secondary-color);
    animation: expandWidth 0.8s ease-out;
}

@keyframes expandWidth {
    from { width: 0; }
    to { width: 50px; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Rows and Groups */
.form-row {
    display: flex;
    gap: 1.25rem;
    margin-bottom: 1.25rem;
}

.form-group {
    flex: 1;
    position: relative;
}

.form-group.full-width {
    flex: 100%;
}

/* Labels */
label {
    display: block;
    color: var(--text-color);
    font-weight: 500;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

/* Input Styles */
input, select, textarea {
    width: 100%;
    padding: 0.75rem 0.9375rem;
    border: 0.125rem solid var(--border-color);
    border-radius: 0.625rem;
    font-size: 1rem;
    font-family: var(--primary-font);
    background: #fafafa;
    transition: var(--transition);
    outline: none;
}

input:focus, select:focus, textarea:focus {
    border-color: var(--secondary-color);
    background: var(--white);
    box-shadow: var(--shadow-focus);
    transform: translateY(-0.125rem);
}

input:valid, select:valid, textarea:valid {
    border-color: var(--success-color);
}

/* Textarea */
textarea {
    resize: vertical;
    min-height: 5rem;
    max-height: 9.375rem;
}

/* File Input */
input[type="file"] {
    padding: 0.5rem;
    background: var(--white);
    border: 0.125rem dashed var(--secondary-color);
    cursor: pointer;
}

input[type="file"]:hover {
    background: var(--primary-color);
}

/* Select Dropdown */
select {
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1rem;
    padding-right: 2.5rem;
}

/* Error Styles */
.error {
    border-color: var(--error-color) !important;
    background: #fff5f5 !important;
    animation: shake 0.3s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-0.3125rem); }
    75% { transform: translateX(0.3125rem); }
}

.error-message {
    color: var(--error-color);
    font-size: 0.85rem;
    margin-top: 0.3125rem;
    display: none;
    font-weight: 500;
}

/* Buttons */
.form-buttons {
    display: flex;
    gap: 0.9375rem;
    justify-content: center;
    margin-top: 2.5rem;
    padding-top: 1.875rem;
    border-top: 0.0625rem solid #eee;
}

.btn {
    padding: 0.75rem 1.875rem;
    border: none;
    border-radius: 0.625rem;
    font-size: 1rem;
    font-weight: 600;
    font-family: var(--primary-font);
    cursor: pointer;
    transition: var(--transition);
    min-width: 9.375rem;
}

.btn-primary {
    background: var(--button-gradient);
    color: var(--white);
    box-shadow: 0 0.25rem 0.9375rem rgba(3, 108, 226, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0077ff, #005bb5);
    transform: translateY(-0.125rem);
    box-shadow: 0 0.375rem 1.25rem rgba(3, 108, 226, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 0.125rem solid var(--border-color);
}

.btn-secondary:hover {
    background: #e9ecef;
    border-color: var(--secondary-color);
    color: var(--secondary-color);
    transform: translateY(-0.125rem);
}

.btn:active {
    transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: var(--margin-medium);
        border-radius: var(--margin-large);
    }

    .form-header {
        padding: var(--padding-xl);
    }

    .form-header h1 {
        font-size: var(--font-size-xxl);
    }

    .student-form {
        padding: var(--padding-xl);
    }

    .form-row {
        flex-direction: column;
        gap: var(--margin-large);
    }

    .form-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 250px;
    }
}

@media (max-width: 480px) {
    body {
        padding: var(--padding-medium);
    }

    .form-header h1 {
        font-size: 1.8rem;
    }

    .student-form {
        padding: var(--padding-large);
    }

    input, select, textarea {
        padding: var(--padding-medium) var(--padding-medium);
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Loading Animation */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading .btn-primary::after {
    content: '';
    width: 16px;
    height: 16px;
    margin-left: 10px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    display: inline-block;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success Animation */
.success {
    border-color: var(--success-color) !important;
    background: #f0fff4 !important;
}

/* Hover Effects */
.form-group:hover input,
.form-group:hover select,
.form-group:hover textarea {
    border-color: var(--secondary-color);
}

/* Focus Within Effect */
.form-group:focus-within label {
    color: var(--secondary-color);
    transform: translateY(-2px);
    transition: var(--transition-normal);
}