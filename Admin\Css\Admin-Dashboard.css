        :root {
            --primary-color: #edf6ff;
            --secondary-color: #036ce2;
            --tertiary-color: #C5E0FE;
            --primary-font: 'Poppins', sans-serif;
            --white: #ffffff;
            --light-gray: #f8f9fa;
            --dark-gray: #2c3e50;
            --success-color: #27ae60;
            --danger-color: #e74c3c;
            --shadow: 0 0 10px -2px rgba(47, 47, 47, 0.357);
            --border-radius: 1.4rem;
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            font-size: 14px;
        }

        body {
            font-family: var(--primary-font);
            background-color: var(--primary-color);
            min-height: 100vh;
            line-height: 1.6;
        }

        /* Header Styles */
        .dashboard-header {
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-evenly;
            width: 100%;
            height: 100%;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;

        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .header-logo {
            width: 4.4rem;
            height: 4rem;
            border-radius: 50%;
            /* border: 2px solid var(--secondary-color); */
            padding: 2px;
        }

        .header-title {
            color: var(--dark-gray);
            font-size: 1.7rem;
            font-weight: 700;
            text-transform: capitalize;
        }

        .header-right {
            display: flex;
            align-items: center;
        }

        .logout-btn {
            background: var(--secondary-color);
            color: var(--white);
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 0.6rem;
            cursor: pointer;
            text-decoration: none;
            font-size: 1rem;
            font-weight: 600;
            font-family: var(--primary-font);
            transition: var(--transition);
            box-shadow: 0 2px 5px rgba(3, 108, 226, 0.3);
        }

        .logout-btn:hover {
            background: #0077ff;
            transform: scale(1.02);
            box-shadow: 0 4px 10px rgba(3, 108, 226, 0.4);
        }

        /* Main Container */
        .dashboard-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        /* Welcome Section */
        .welcome-section {
            background: linear-gradient(135deg, var(--white), var(--tertiary-color));
            border-radius: var(--border-radius);
            padding: 2.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
            text-align: center;
            border: 2px solid rgba(3, 108, 226, 0.1);
            position: relative;
            overflow: hidden;
        }

        .welcome-title {
            color: var(--dark-gray);
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-transform: capitalize;
        }

        .welcome-subtitle {
            color: var(--secondary-color);
            font-size: 1.2rem;
            font-weight: 500;
            margin-bottom: 1rem;
        }

        .welcome-message {
            color: var(--dark-gray);
            font-size: 1rem;
            font-weight: 400;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
            padding: 1rem;
            /* border: 2px solid black; */
        }

        .dashboard-card {
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 2rem;
            /* width:95%; */
            /* max-width: 350px; */
            box-shadow: var(--shadow);
            transition: var(--transition);
            border: 2px solid rgba(3, 108, 226, 0.1);
            position: relative;
            overflow: hidden;
            /* border: 3px solid black; */
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(47, 47, 47, 0.2);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .card-icon {
            width: 4rem;
            height: 4rem;
            background: var(--secondary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.7rem;
            /* border: 2px solid black; */
            color: var(--white);
            box-shadow: 0 4px 10px rgba(3, 108, 226, 0.3);
        }


        .card-title {
            color: var(--dark-gray);
            font-size: 1.3rem;
            /* font-weight: 600; */
            text-transform: capitalize;
        }

        .card-content {
            color: var(--dark-gray);
            font-size: 1rem;
            line-height: 1.6;
        }

        .card-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .stat-item {
            background: var(--light-gray);
            padding: .5rem;
            border-radius: 0.6rem;
            text-align: center;
            border: 1px solid rgba(3, 108, 226, 0.1);
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--secondary-color);
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--dark-gray);
            margin-top: 0.25rem;
            font-weight: 500;
        }

        /* Card Actions */
        .card-actions {
            display: flex;
            gap: 0.8rem;
            margin-top: 1.5rem;
            padding-top: 1rem;
            border-top: 1px solid rgba(3, 108, 226, 0.1);
        }

        .action-btn {
            flex: 1;
            padding: 0.7rem 1rem;
            border: none;
            border-radius: 0.6rem;
            font-family: var(--primary-font);
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-transform: capitalize;
        }

        .view-btn {
            background: linear-gradient(135deg, var(--tertiary-color), rgba(3, 108, 226, 0.1));
            color: var(--secondary-color);
            border: 1px solid rgba(3, 108, 226, 0.2);
        }

        .view-btn:hover {
            background: linear-gradient(135deg, var(--secondary-color), #0077ff);
            color: var(--white);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(3, 108, 226, 0.3);
        }

        .add-btn {
            background: linear-gradient(135deg, var(--secondary-color), #0077ff);
            color: var(--white);
            border: 1px solid var(--secondary-color);
        }

        .add-btn:hover {
            background: linear-gradient(135deg, #0077ff, #005bb5);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(3, 108, 226, 0.4);
        }

        .action-btn:active {
            transform: translateY(0);
        }

        .action-btn i {
            font-size: 0.9rem;
        }

        /* Responsive Design */
        @media (max-width: 720px) {
            html {
                font-size: 12px;
            }

            .header-content {
                /* flex-direction: column; */
                text-align: center;
                gap: 1.5rem;
            }

            .header-title {
                font-size: 1.6rem;
            }

            .dashboard-container {
                margin: 1rem auto;
                padding: 0 1rem;
            }

            .welcome-section {
                padding: 2rem 1.5rem;
            }

            .welcome-title {
                font-size: 2rem;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .dashboard-card {
                padding: 1.5rem;
            }

            .card-stats {
                grid-template-columns: 1fr;
            }

            .card-actions {
                flex-direction: column;
                gap: 0.6rem;
            }

            .action-btn {
                padding: 0.6rem 0.8rem;
                font-size: 0.85rem;
            }
        }

        @media (min-width: 250px) and (max-width: 460px) {
            html {
                font-size: 14px;
            }

            .header-content {
                padding: 0 1rem;
            }

            .header-logo {
                width: 40px;
                height: 40px;
            }

            .header-title {
                font-size: 1.4rem;
            }

            .user-info {
                text-align: center;
            }

            .logout-btn {
                padding: 0.6rem 1.2rem;
                font-size: 0.9rem;
            }

            .welcome-section {
                padding: 1.5rem 1rem;
                margin-bottom: 1.5rem;
            }

            .welcome-title {
                font-size: 1.8rem;
            }

            .welcome-subtitle {
                font-size: 1rem;
            }

            .dashboard-card {
                padding: 1.25rem;
            }

            .card-icon {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }

            .card-title {
                font-size: 1.1rem;
            }

            .card-actions {
                gap: 0.6rem;
            }

            .action-btn {
                padding: 0.6rem 0.8rem;
                font-size: 0.85rem;
            }
        }

        @media (max-width: 250px) {
            html {
                font-size: 8px;
            }

            .header-content {
                padding: 0 0.5rem;
            }

            .welcome-section {
                padding: 1rem;
            }

            .dashboard-card {
                padding: 1rem;
            }
        }

        /* Loading Animation */
        .loading {
            opacity: 0.7;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 30px;
            height: 30px;
            margin: -15px 0 0 -15px;
            border: 3px solid transparent;
            border-top: 3px solid var(--secondary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Accessibility */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        @media (prefers-contrast: high) {

            .dashboard-card,
            .welcome-section {
                border: 2px solid var(--dark-gray);
            }

            .logout-btn {
                border: 2px solid var(--white);
            }
        }

        /* Focus styles for accessibility */
        .logout-btn:focus {
            outline: 2px solid var(--secondary-color);
            outline-offset: 2px;
        }