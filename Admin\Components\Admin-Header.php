<?php
$admin_username = isset($_SESSION['admin_username']) ? $_SESSION['admin_username'] : 'Admin';
?>
<style>
    
</style>
<div class="dashboard-header">
    <div class="header-content">
        <div class="header-left">
            <a href="Dashboard"><img
                    src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQHNB6WJ50MG_87iapEhrPegc2BIDDF3dR-kQ&s"
                    alt="SSASIT Logo" class="header-logo"></a>
            <h1 class="header-title">SSASIT</h1>
        </div>
        <div class="header-right">
            <div class="user-info">
            </div>
            <a href="?logout=1" class="logout-btn" onclick="return confirmLogout()">
                Logout
            </a>
        </div>
    </div>
</div>

<script>
    window.addEventListener('scroll', function () {
        const header = document.querySelector('.dashboard-header');
        header.style.transition = 'var(--transition)';
        if (window.scrollY > 25) {
            // header.style.boxShadow = '0 4px 10px rgba(3, 108, 226, 0.3)';
            header.style.backgroundColor = 'var(--white)';
            header.style.height = '70px';
        }
        else if (window.scrollY < 30) {
            header.style.backgroundColor = 'transparent';
            header.style.height = '80px';
        }
    });
    // Add loading animation to cards on hover
    // Logout confirmation function
    function confirmLogout() {
        return confirm('Are you sure you want to logout? You will be redirected to the login page.');
    }

    const logoutBtn = document.querySelector('.logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('mousedown', function () {
            this.style.transform = 'scale(0.98)';
        });

        logoutBtn.addEventListener('mouseup', function () {
            this.style.transform = 'scale(1.02)';
        });

        logoutBtn.addEventListener('mouseleave', function () {
            this.style.transform = 'scale(1)';
        });
    }
</script>