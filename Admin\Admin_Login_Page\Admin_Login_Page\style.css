* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    background: #F6F9FE;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.login-container {
    width: 850px;
    max-width: 95%;
}

.login-card {
    display: flex;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0px 4px 20px rgba(0,0,0,0.1);
}

/* Left Image Section */
.login-image {
    flex: 1;
    background: white;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
}

.login-image img {
    width: 100%;
    max-width: 100%;
    height: auto;
}

/* Right Form Section */
.login-form {
    flex: 1;
    padding: 40px;
}

.icon-user-top img {
    width: 50px;
    display: block;
    margin: 0 auto 10px;
}

h1 {
    text-align: center;
    font-weight: 600;
    margin-bottom: 20px;
}

label {
    font-size: 14px;
    margin-top: 10px;
    display: block;
    font-weight: 500;
}

/* Input with icon */
.input-icon {
    position: relative;
    margin-top: 5px;
}

.left-icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 15px;
    width: 18px;
    height: 18px;
    opacity: 0.7;
}

.input-icon input {
    width: 100%;
    padding: 10px 45px 10px 45px;
    border: 2px solid #cddaf6;
    border-radius: 25px;
    outline: none;
    font-size: 14px;
}

.eye-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 18px;
    height: 18px;
    cursor: pointer;
    opacity: 0.7;
}

.forgot {
    display: block;
    text-align: right;
    font-size: 12px;
    color: #1d6aff;
    margin-top: 5px;
    text-decoration: none;
}

.login-btn {
    width: 100%;
    padding: 12px;
    margin-top: 20px;
    background: #1d6aff;
    color: white;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.4s ease;
}

.login-btn:hover {
    background: #0f57d1;
}

/* Tablet view */
@media (max-width: 1024px) {
    .login-card {
        flex-direction: column;
        max-width: 600px;
        margin: auto;
    }

    .login-image {
        padding: 20px;
    }

    .login-image img {
        max-width: 70%;
    }

    .login-form {
        padding: 25px;
    }
}

/* Phone view */
@media (max-width: 600px) {
    body {
        padding: 15px;
        height: auto;
    }

    .login-card {
        flex-direction: column;
        width: 100%;
    }

    .login-image {
        display: none; /* hide image for mobile for cleaner layout */
    }

    .login-form {
        padding: 20px;
    }

    .input-icon input {
        font-size: 16px;
        padding: 12px 45px 12px 45px;
    }

    .login-btn {
        font-size: 18px;
        padding: 14px;
    }

    h1 {
        font-size: 24px;
    }
}
