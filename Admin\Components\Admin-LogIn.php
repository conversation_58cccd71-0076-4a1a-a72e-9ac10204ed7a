<?php

$error = "";

if (isset($_POST['username']) && isset($_POST['password'])) {

    $Username = $_POST['username'];
    $Password = $_POST['password'];

    if ($Username == 'admin' && $Password == 'Admin@123') {

        session_start();

        $_SESSION['User_Logged-In'] = true;
        $_SESSION['LAST_ACTIVITY'] = time();

        header('Location: ../Pages/Admin-Dashboard.php');

        exit();
    } else {
        $error = "Invalid Username or Password";
    }
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSASIT-Authentication</title>
    <link rel="stylesheet" href="../Css/Admin-Login.css">
    <link rel="stylesheet" href="../index.css">
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/fontawesome.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/whiteboard-semibold.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/thumbprint-light.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/slab-press-regular.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/slab-regular.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/sharp-duotone-thin.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/sharp-duotone-solid.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/sharp-duotone-regular.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/sharp-duotone-light.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/sharp-thin.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/sharp-solid.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/sharp-regular.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/sharp-light.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/notdog-duo-solid.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/notdog-solid.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/jelly-fill-regular.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/jelly-duo-regular.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/jelly-regular.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/etch-solid.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/duotone-thin.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/duotone.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/duotone-regular.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/duotone-light.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/thin.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/solid.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/regular.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/light.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/brands.css">

    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v7.0.0/css/chisel-regular.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&transform=swap"
        rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
</head>

<body>
    <div class="auth">
        <div class="login-image">

        </div>
        <div class="login-card">
            <div class="login-icon">
                <img src="../Public/Images/user.png" alt="user">
                <h1>admin login</h1>
            </div>
            <form action="" class="login-form" method="post">
                <?php if ($error) { ?>
                    <p class="error-msg"><?php echo $error; ?></p>
                <?php } ?>
                <div class="input-group">
                    <i class="fa-solid fa-circle-xmark cross"></i>
                    <label class='login-labels' for="username">Username</label>
                    <input class='login-fields' type="text" name="username" id="username"
                        placeholder="Enter your username" required minlength="3" maxlength="15">
                </div>

                <div class="input-group">
                    <img src="../Public/Images/seen.png" class="eye" alt="">
                    <!-- <i class="fa-jelly-fill fa-regular fa-eye"></i> -->
                    <i class="fa-solid fa-eye-slash"></i>
                    <label class='login-labels' for="password">Password</label>
                    <input class='login-fields' type="password" name="password" id="password"
                        placeholder="Enter your Password" required
                        pattern="(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@_])[A-Za-z0-9_@]{8,15}"
                        title="Password must be 8 to 15 characters long and contain at least One Upper case and One Lower case letter and at least One Number and _ @">
                </div>
                <a class='forgot-password' href="#">Forgot Password?</a>
                <input type="submit" value="Login">
            </form>
        </div>
    </div>
</body>
<script>
    const username = document.querySelector('#username');
    const password = document.querySelector('#password');
    const cross = document.querySelector('.fa-circle-xmark');
    const eye = document.querySelector('.eye');
    const eyeSlash = document.querySelector('.fa-eye-slash');
    const loginForm = document.querySelector('.login-form');



    username.addEventListener('input', () => {
        cross.style.transform = 'scale(0)';

        if (username.value) {
            cross.style.transform = 'scale(1)';
            cross.addEventListener('click', () => {
                username.value = '';
                cross.style.transform = 'scale(0)';
            });
        } else {
            cross.style.transform = 'scale(0)';
        }
    });

    loginForm.addEventListener('submit', (e) => {
        e.preventDefault();
        loginForm.submit();
    });

    eye.addEventListener('click', () => {
        password.type = 'text';
        eye.style.transform = 'scale(0)';
        eyeSlash.style.transform = 'scale(1)';
    });
    eyeSlash.addEventListener('click', () => {
        password.type = 'password';
        eye.style.transform = 'scale(1)';
        eyeSlash.style.transform = 'scale(0)';
    });
</script>

</html>