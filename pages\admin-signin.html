<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="SSASIT Admin Portal - Secure Administrator Access">
    <link rel="shortcut icon" href="../public/favicon/SSASIT.png" type="image/x-icon">
    <title>Admin Sign In - SSASIT</title>
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            font-size: 16px;
            scroll-behavior: smooth;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
            background: rgba(0, 0, 255, 0.648);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            line-height: 1.6;
            user-select: none;
        }

        /* Main Container */
        .signin-container {
            background: rgba(255, 255, 255, 0.156); /*rgba(255, 255, 255, 0.9)*/
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(40px);
            border-radius: 24px;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(0, 0, 0, 0.05);
            padding: 3rem 2.5rem;
            width: 100%;
            max-width: 420px;
            position: relative;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .signin-container:hover {
            transform: translateY(-5px);
            box-shadow:
                0 35px 70px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(0, 0, 0, 0.08);
        }

        /* Logo Section */
        .logo-section {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .logo-container {
            position: relative;
            display: inline-block;
            margin-bottom: 1.5rem;
        }

        .logo {
            width: 90px;
            height: 90px;
            border-radius: 50%;
            border: 3px solid #f0f0f0;
            padding: 4px;
            background: white;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            object-fit: cover;
        }

        .logo:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .signin-title {
            color: #2c3e50;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            letter-spacing: -0.5px;
        }

        .signin-subtitle {
            color: #7f8c8d;
            font-size: 0.95rem;
            font-weight: 400;
        }

        /* Form Styles */
        .signin-form {
            width: 100%;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-label {
            display: block;
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .input-container {
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 1rem 1.25rem;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            font-size: 1rem;
            background: rgba(248, 249, 250, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            outline: none;
            color: #2c3e50;
        }

        .form-input:focus {
            border-color: #3498db;
            background: rgba(255, 255, 255, 0.9);
            box-shadow:
                0 0 0 3px rgba(52, 152, 219, 0.1),
                0 4px 12px rgba(52, 152, 219, 0.15);
            transform: translateY(-2px);
        }

        .form-input:valid:not(:placeholder-shown) {
            border-color: #27ae60;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-input::placeholder {
            color: #95a5a6;
            font-weight: 400;
        }

        /* Password Toggle */
        .password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            color: #7f8c8d;
            font-size: 1.1rem;
            padding: 0.25rem;
            border-radius: 4px;
            transition: color 0.3s ease, background-color 0.3s ease;
        }

        .password-toggle:hover {
            color: #3498db;
            background-color: rgba(52, 152, 219, 0.1);
        }

        .password-toggle:focus {
            outline: 2px solid #3498db;
            outline-offset: 2px;
        }

        /* Button Styles */
        .signin-btn {
            width: 100%;
            padding: 1rem 1.5rem;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-top: 1rem;
            position: relative;
            overflow: hidden;
        }

        .signin-btn:hover:not(:disabled) {
            background: linear-gradient(135deg, #2980b9, #1f4e79);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
        }

        .signin-btn:active {
            transform: translateY(0);
        }

        .signin-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }

        /* Error Messages */
        .error-message {
            color: #e74c3c;
            font-size: 0.85rem;
            margin-top: 0.5rem;
            display: none;
            font-weight: 500;
        }

        .form-input.error {
            border-color: #e74c3c;
            background: rgba(231, 76, 60, 0.05);
        }

        /* Success State */
        .form-input.success {
            border-color: #27ae60;
            background: rgba(39, 174, 96, 0.05);
        }

        /* Footer */
        .footer-text {
            text-align: center;
            margin-top: 2rem;
            color: #7f8c8d;
            font-size: 0.8rem;
            line-height: 1.4;
        }

        /* Loading State */
        .loading {
            opacity: 0.7;
            pointer-events: none;
        }

        .loading .signin-btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Responsive Design - Mobile First */
        @media (max-width: 480px) {
            body {
                padding: 0.5rem;
            }

            .signin-container {
                padding: 2rem 1.5rem;
                border-radius: 20px;
                max-width: 100%;
            }

            .signin-title {
                font-size: 1.75rem;
            }

            .logo {
                width: 75px;
                height: 75px;
            }

            .form-input {
                padding: 0.875rem 1rem;
                font-size: 16px;
                /* Prevent zoom on iOS */
            }

            .signin-btn {
                padding: 0.875rem 1.25rem;
                font-size: 1rem;
            }
        }

        @media (max-width: 360px) {
            .signin-container {
                padding: 1.5rem 1rem;
            }

            .signin-title {
                font-size: 1.5rem;
            }

            .logo {
                width: 65px;
                height: 65px;
            }
        }

        /* Tablet Styles */
        @media (min-width: 481px) and (max-width: 768px) {
            .signin-container {
                max-width: 450px;
                padding: 3.5rem 3rem;
            }
        }

        /* Desktop Styles */
        @media (min-width: 769px) {
            .signin-container {
                max-width: 480px;
            }
        }

        /* Accessibility */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        @media (prefers-contrast: high) {
            .signin-container {
                background: white;
                border: 2px solid #000;
            }

            .form-input {
                border-color: #000;
                background: white;
            }

            .signin-btn {
                background: #000;
            }
        }

        /* Focus styles for keyboard navigation */
        .form-input:focus,
        .signin-btn:focus {
            outline: 2px solid #3498db;
            outline-offset: 2px;
        }
    </style>
</head>

<body>
    <div class="signin-container">
        <div class="logo-section">
            <div class="logo-container">
                <img src="../public/favicon/SSASIT.png" alt="SSASIT Logo" class="logo">
            </div>
            <h1 class="signin-title">Admin Portal</h1>
            <p class="signin-subtitle">Shree Swami Atmanand Saraswati Institute of Technology</p>
        </div>

        <form class="signin-form" id="signinForm" novalidate>
            <div class="form-group">
                <label for="username" class="form-label">Username / Email</label>
                <div class="input-container">
                    <input type="text" id="username" name="username" class="form-input"
                        placeholder="Enter your username or email" required autocomplete="username"
                        autocapitalize="none" spellcheck="false">
                    <div class="error-message" id="username-error"></div>
                </div>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">Password</label>
                <div class="input-container">
                    <input type="password" id="password" name="password" class="form-input"
                        placeholder="Enter your password" required autocomplete="current-password">
                    <button type="button" class="password-toggle" id="passwordToggle"
                        aria-label="Toggle password visibility">
                        👁️
                    </button>
                    <div class="error-message" id="password-error"></div>
                </div>
            </div>

            <button type="submit" class="signin-btn" id="submitBtn">
                Sign In
            </button>
        </form>

        <div class="footer-text">
            <strong>Authorized Personnel Only</strong><br>
            © 2024 SSASIT. All rights reserved.
        </div>
    </div>

    <script>
        // DOM Elements
        const form = document.getElementById('signinForm');
        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');
        const passwordToggle = document.getElementById('passwordToggle');
        const submitBtn = document.getElementById('submitBtn');
        const usernameError = document.getElementById('username-error');
        const passwordError = document.getElementById('password-error');

        // Validation Rules
        const validationRules = {
            username: {
                minLength: 3,
                maxLength: 50,
                pattern: /^[a-zA-Z0-9@._-]+$/,
                message: 'Username must be 3-50 characters and contain only letters, numbers, @, ., _, or -'
            },
            password: {
                minLength: 6,
                maxLength: 100,
                message: 'Password must be at least 6 characters long'
            }
        };

        // Utility Functions
        function showError(errorElement, message) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
            errorElement.parentElement.querySelector('.form-input').classList.add('error');
            errorElement.parentElement.querySelector('.form-input').classList.remove('success');
        }

        function hideError(errorElement) {
            errorElement.textContent = '';
            errorElement.style.display = 'none';
            errorElement.parentElement.querySelector('.form-input').classList.remove('error');
        }

        function showSuccess(input) {
            input.classList.add('success');
            input.classList.remove('error');
        }

        function validateField(input, rules, errorElement) {
            const value = input.value.trim();

            if (!value) {
                showError(errorElement, `${input.name.charAt(0).toUpperCase() + input.name.slice(1)} is required`);
                return false;
            }

            if (value.length < rules.minLength) {
                showError(errorElement, `Minimum ${rules.minLength} characters required`);
                return false;
            }

            if (value.length > rules.maxLength) {
                showError(errorElement, `Maximum ${rules.maxLength} characters allowed`);
                return false;
            }

            if (rules.pattern && !rules.pattern.test(value)) {
                showError(errorElement, rules.message);
                return false;
            }

            hideError(errorElement);
            showSuccess(input);
            return true;
        }

        function validateForm() {
            const isUsernameValid = validateField(usernameInput, validationRules.username, usernameError);
            const isPasswordValid = validateField(passwordInput, validationRules.password, passwordError);

            return isUsernameValid && isPasswordValid;
        }

        function setLoadingState(loading) {
            if (loading) {
                submitBtn.disabled = true;
                submitBtn.textContent = 'Signing In...';
                form.classList.add('loading');
            } else {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Sign In';
                form.classList.remove('loading');
            }
        }

        // Password Toggle Functionality
        function togglePasswordVisibility() {
            const isPassword = passwordInput.type === 'password';
            passwordInput.type = isPassword ? 'text' : 'password';
            passwordToggle.textContent = isPassword ? '🙈' : '👁️';
            passwordToggle.setAttribute('aria-label', isPassword ? 'Hide password' : 'Show password');

            // Add visual feedback
            passwordToggle.style.transform = 'scale(0.9)';
            setTimeout(() => {
                passwordToggle.style.transform = 'scale(1)';
            }, 150);
        }

        // Event Listeners
        passwordToggle.addEventListener('click', togglePasswordVisibility);

        // Real-time validation
        usernameInput.addEventListener('input', function () {
            if (this.value.trim()) {
                validateField(this, validationRules.username, usernameError);
            } else {
                hideError(usernameError);
                this.classList.remove('success', 'error');
            }
        });

        usernameInput.addEventListener('blur', function () {
            if (this.value.trim()) {
                validateField(this, validationRules.username, usernameError);
            }
        });

        passwordInput.addEventListener('input', function () {
            if (this.value.trim()) {
                validateField(this, validationRules.password, passwordError);
            } else {
                hideError(passwordError);
                this.classList.remove('success', 'error');
            }
        });

        passwordInput.addEventListener('blur', function () {
            if (this.value.trim()) {
                validateField(this, validationRules.password, passwordError);
            }
        });

        // Form submission
        form.addEventListener('submit', function (e) {
            e.preventDefault();

            if (!validateForm()) {
                // Focus on first error field
                const firstError = form.querySelector('.form-input.error');
                if (firstError) {
                    firstError.focus();
                }
                return false;
            }

            setLoadingState(true);

            // Simulate form submission (replace with actual submission logic)
            setTimeout(() => {
                setLoadingState(false);
                alert('Form validation successful! Ready for backend integration.');

                // Log form data for demonstration
                console.log('Form Data:', {
                    username: usernameInput.value.trim(),
                    password: passwordInput.value
                });
            }, 2000);
        });

        // Auto-focus on username field when page loads
        document.addEventListener('DOMContentLoaded', function () {
            usernameInput.focus();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function (e) {
            // Alt + U to focus username
            if (e.altKey && e.key.toLowerCase() === 'u') {
                e.preventDefault();
                usernameInput.focus();
            }

            // Alt + P to focus password
            if (e.altKey && e.key.toLowerCase() === 'p') {
                e.preventDefault();
                passwordInput.focus();
            }

            // Ctrl + Enter to submit form
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                form.dispatchEvent(new Event('submit'));
            }
        });

        // Enhanced touch support for mobile devices
        if ('ontouchstart' in window) {
            // Add touch feedback for buttons
            [submitBtn, passwordToggle].forEach(element => {
                element.addEventListener('touchstart', function () {
                    this.style.transform = 'scale(0.95)';
                });

                element.addEventListener('touchend', function () {
                    this.style.transform = '';
                });
            });

            // Prevent double-tap zoom on inputs
            [usernameInput, passwordInput].forEach(input => {
                input.addEventListener('touchend', function (e) {
                    e.preventDefault();
                    this.focus();
                });
            });
        }

        // Security: Clear sensitive data on page unload
        window.addEventListener('beforeunload', function () {
            passwordInput.value = '';
        });

        // Prevent form resubmission on page refresh
        if (window.history.replaceState) {
            window.history.replaceState(null, null, window.location.href);
        }

        // Add visual feedback for successful field completion
        function addSuccessAnimation(input) {
            input.style.transform = 'scale(1.02)';
            setTimeout(() => {
                input.style.transform = '';
            }, 200);
        }

        // Enhanced success feedback
        usernameInput.addEventListener('input', function () {
            if (this.classList.contains('success')) {
                addSuccessAnimation(this);
            }
        });

        passwordInput.addEventListener('input', function () {
            if (this.classList.contains('success')) {
                addSuccessAnimation(this);
            }
        });
    </script>
</body>

</html>