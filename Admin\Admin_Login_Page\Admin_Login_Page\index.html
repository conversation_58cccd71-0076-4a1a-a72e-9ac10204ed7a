<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login</title>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>
<body>

<div class="login-container">
    <div class="login-card">
        <!-- Left Image -->
        <div class="login-image">
            <img src="login-illustration.png" alt="Login Illustration">
        </div>

        <!-- Right Form -->
        <div class="login-form">
            <div class="icon-user-top">
                <img src="user.png" alt="User Icon">
            </div>
            <h1>Admin Login</h1>

            <!-- Username -->
            <label>Username</label>
            <div class="input-icon">
                <img src="user.png" alt="User Icon" class="left-icon">
                <input type="text" placeholder="Enter username">
            </div>

            <!-- Password -->
            <label>Password</label>
            <div class="input-icon">
                <img src="password-icon.png" alt="Password Icon" class="left-icon lock-icon">
                <input type="password" id="password" placeholder="Enter password">
                <img src="seen.png" alt="Show Password" class="eye-icon" id="togglePassword">
            </div>

            <!-- Forgot Link -->
            <a href="#" class="forgot">Forgot password?</a>

            <!-- Login Button -->
            <button class="login-btn">LOGIN</button>
        </div>
    </div>
</div>

<!-- Password Toggle Script -->
<script>
    document.getElementById('togglePassword').addEventListener('click', function () {
    const passwordField = document.getElementById('password');

    if (passwordField.type === 'password') {
        passwordField.type = 'text'; // Show password
    } else {
        passwordField.type = 'password'; // Hide password
    }
    });
</script>

</body>
</html> 
