<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Page Not Found | SSASIT</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            height:fit-content;
            display: flex;
            align-items: center;
            justify-content: center;
            /* overflow: hidden; */
            position: relative;
            /* border: 2px solid red; */
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Floating Elements */
        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
            font-size: 2rem;
        }

        .floating-element:nth-child(2) {
            top: 20%;
            right: 10%;
            animation-delay: 1s;
            font-size: 1.5rem;
        }

        .floating-element:nth-child(3) {
            bottom: 20%;
            left: 15%;
            animation-delay: 2s;
            font-size: 1.8rem;
        }

        .floating-element:nth-child(4) {
            bottom: 30%;
            right: 20%;
            animation-delay: 3s;
            font-size: 2.2rem;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* Main Container */
        .container {
            text-align: center;
            color: white;
            z-index: 10;
            position: relative;
            max-width: 600px;
            padding: 2rem;
        }

        /* 404 Number */
        .error-number {
            font-size: 8rem;
            font-weight: 900;
            margin-bottom: 1rem;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
            animation: bounce 2s ease-in-out infinite;
            background: linear-gradient(45deg, #fff, #f0f0f0, #fff);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: bounce 2s ease-in-out infinite, shimmer 3s ease-in-out infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        @keyframes shimmer {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Cute Character */
        .character {
            font-size: 4rem;
            margin: 1rem 0;
            animation: wiggle 1.5s ease-in-out infinite;
            display: inline-block;
        }

        @keyframes wiggle {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(-5deg); }
            75% { transform: rotate(5deg); }
        }

        /* Title and Message */
        .title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            animation: fadeInUp 1s ease-out 0.5s both;
        }

        .message {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
            animation: fadeInUp 1s ease-out 1s both;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Buttons */
        .buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            animation: fadeInUp 1s ease-out 1.5s both;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .error-number {
                font-size: 6rem;
            }

            .title {
                font-size: 2rem;
            }

            .message {
                font-size: 1rem;
            }

            .character {
                font-size: 3rem;
            }

            .buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 200px;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 1rem;
            }

            .error-number {
                font-size: 4rem;
            }

            .title {
                font-size: 1.5rem;
            }

            .character {
                font-size: 2.5rem;
            }
        }

        /* Loading Animation */
        .loading-dots {
            display: inline-block;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0%, 80%, 100% { opacity: 0; }
            40% { opacity: 1; }
        }

        .loading-dots:nth-child(2) { animation-delay: 0.2s; }
        .loading-dots:nth-child(3) { animation-delay: 0.4s; }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-element">🔍</div>
        <div class="floating-element">📄</div>
        <div class="floating-element">❓</div>
        <div class="floating-element">🏠</div>
    </div>

    <div class="container">
        <div class="error-number">404</div>
        <div class="character">🤖</div>
        <h1 class="title">Oops! Page Not Found</h1>
        <p class="message">
            The page you're looking for seems to have wandered off into the digital void. 
            Don't worry, even our cute robot friend is confused! 
            <br><br>
            Let's get you back on track<span class="loading-dots">.</span><span class="loading-dots">.</span><span class="loading-dots">.</span>
        </p>
        <div class="buttons">
            <a href="javascript:history.back()" class="btn btn-secondary">
                ← Go Back
            </a>
            <a href="Authentication" class="btn btn-primary">
                🏠 Home Page
            </a>
        </div>
    </div>

    <script>
        // Add some interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            const character = document.querySelector('.character');
            const characters = ['🤖', '😅', '🤔', '😊', '🙃', '😎'];
            let currentIndex = 0;

            // Change character every 3 seconds
            setInterval(() => {
                currentIndex = (currentIndex + 1) % characters.length;
                character.textContent = characters[currentIndex];
            }, 3000);

            // Add click effect to character
            character.addEventListener('click', function() {
                this.style.animation = 'none';
                setTimeout(() => {
                    this.style.animation = 'wiggle 1.5s ease-in-out infinite';
                }, 10);
                
                // Random character on click
                const randomIndex = Math.floor(Math.random() * characters.length);
                this.textContent = characters[randomIndex];
            });

            // Add floating elements dynamically
            const floatingContainer = document.querySelector('.floating-elements');
            const emojis = ['⭐', '✨', '💫', '🌟', '💎', '🎈', '🎉', '🎊'];
            
            setInterval(() => {
                const emoji = document.createElement('div');
                emoji.className = 'floating-element';
                emoji.textContent = emojis[Math.floor(Math.random() * emojis.length)];
                emoji.style.left = Math.random() * 100 + '%';
                emoji.style.top = Math.random() * 100 + '%';
                emoji.style.fontSize = (Math.random() * 1.5 + 1) + 'rem';
                emoji.style.animationDuration = (Math.random() * 3 + 3) + 's';
                
                floatingContainer.appendChild(emoji);
                
                // Remove after animation
                setTimeout(() => {
                    if (emoji.parentNode) {
                        emoji.parentNode.removeChild(emoji);
                    }
                }, 6000);
            }, 2000);

            // Add keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    document.querySelector('.btn-primary').click();
                } else if (e.key === 'Escape') {
                    history.back();
                }
            });
        });
    </script>
</body>
</html>
