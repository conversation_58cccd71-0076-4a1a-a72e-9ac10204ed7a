<?php
/**
 * Email Configuration for SSASIT Admin System
 *
 * This file contains email settings and functions for sending emails
 * using PHPMailer library with proper error handling and security.
 *
 * <AUTHOR> Admin Team
 * @version 1.0
 */

// Prevent direct access
if (!defined('ADMIN_ACCESS')) {
    die('Direct access not permitted');
}

use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

// Email Configuration Constants
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_SECURE', PHPMailer::ENCRYPTION_STARTTLS);
define('SMTP_USERNAME', '<EMAIL>'); // Change to your email
define('SMTP_PASSWORD', 'your_app_password_here'); // Use App Password, not regular password
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'SSASIT Administration');
define('REPLY_TO_EMAIL', '<EMAIL>');
define('REPLY_TO_NAME', 'SSASIT No Reply');

/**
 * Initialize PHPMailer with configuration
 *
 * @return PHPMailer Configured PHPMailer instance
 * @throws Exception If PHPMailer initialization fails
 */
function initializeMailer() {
    try {
        // Check if PHPMailer is available
        if (!class_exists('PHPMailer\PHPMailer\PHPMailer')) {
            // Try to load via composer autoload
            if (file_exists(__DIR__ . '/../../vendor/autoload.php')) {
                require_once __DIR__ . '/../../vendor/autoload.php';
            } elseif (file_exists(__DIR__ . '/../vendor/autoload.php')) {
                require_once __DIR__ . '/../vendor/autoload.php';
            } else {
                throw new Exception('PHPMailer library not found. Please install via Composer.');
            }
        }

        $mail = new PHPMailer(true);

        // Server settings
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = SMTP_SECURE;
        $mail->Port = SMTP_PORT;

        // Enable verbose debug output (disable in production)
        if (defined('EMAIL_DEBUG') && EMAIL_DEBUG) {
            $mail->SMTPDebug = SMTP::DEBUG_SERVER;
        }

        // Default sender
        $mail->setFrom(FROM_EMAIL, FROM_NAME);
        $mail->addReplyTo(REPLY_TO_EMAIL, REPLY_TO_NAME);

        // Content settings
        $mail->isHTML(true);
        $mail->CharSet = 'UTF-8';

        return $mail;

    } catch (Exception $e) {
        error_log("Email initialization failed: " . $e->getMessage());
        throw new Exception("Email system initialization failed");
    }
}

/**
 * Send email using configured settings
 *
 * @param string $to Recipient email address
 * @param string $toName Recipient name
 * @param string $subject Email subject
 * @param string $body Email body (HTML)
 * @param string $altBody Plain text alternative body
 * @param array $attachments Optional attachments array
 * @return bool True on success, false on failure
 */
function sendEmail($to, $toName, $subject, $body, $altBody = '', $attachments = []) {
    try {
        $mail = initializeMailer();

        // Recipients
        $mail->addAddress($to, $toName);

        // Content
        $mail->Subject = $subject;
        $mail->Body = $body;

        if (!empty($altBody)) {
            $mail->AltBody = $altBody;
        }

        // Add attachments if provided
        if (!empty($attachments)) {
            foreach ($attachments as $attachment) {
                if (is_array($attachment)) {
                    $mail->addAttachment($attachment['path'], $attachment['name'] ?? '');
                } else {
                    $mail->addAttachment($attachment);
                }
            }
        }

        $result = $mail->send();

        if ($result) {
            error_log("Email sent successfully to: " . $to);
            return true;
        } else {
            error_log("Email sending failed to: " . $to);
            return false;
        }

    } catch (Exception $e) {
        error_log("Email sending error: " . $e->getMessage());
        return false;
    }
}

/**
 * Send notification email to admin
 *
 * @param string $subject Email subject
 * @param string $message Email message
 * @param string $priority Priority level (high, normal, low)
 * @return bool True on success, false on failure
 */
function sendAdminNotification($subject, $message, $priority = 'normal') {
    $adminEmail = '<EMAIL>'; // Change to actual admin email
    $adminName = 'SSASIT Administrator';

    $htmlBody = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .header { background: #036ce2; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; }
            .priority-high { border-left: 5px solid #e74c3c; }
            .priority-normal { border-left: 5px solid #3498db; }
            .priority-low { border-left: 5px solid #27ae60; }
            .footer { background: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class='header'>
            <h2>SSASIT Admin Notification</h2>
        </div>
        <div class='content priority-{$priority}'>
            <h3>{$subject}</h3>
            <p>{$message}</p>
            <p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>
            <p><strong>Priority:</strong> " . ucfirst($priority) . "</p>
        </div>
        <div class='footer'>
            <p>This is an automated message from SSASIT Administration System</p>
        </div>
    </body>
    </html>";

    $altBody = "SSASIT Admin Notification\n\n{$subject}\n\n{$message}\n\nTime: " . date('Y-m-d H:i:s') . "\nPriority: " . ucfirst($priority);

    return sendEmail($adminEmail, $adminName, "[SSASIT Admin] " . $subject, $htmlBody, $altBody);
}

/**
 * Test email configuration
 *
 * @return array Test results
 */
function testEmailConfig() {
    $results = [
        'success' => false,
        'message' => '',
        'details' => []
    ];

    try {
        // Test PHPMailer initialization
        $mail = initializeMailer();
        $results['details']['initialization'] = 'Success';

        // Test SMTP connection
        if ($mail->smtpConnect()) {
            $results['details']['smtp_connection'] = 'Success';
            $mail->smtpClose();

            // Send test email
            $testResult = sendEmail(
                FROM_EMAIL,
                'Test Recipient',
                'SSASIT Email Configuration Test',
                '<h2>Email Configuration Test</h2><p>If you receive this email, the configuration is working correctly!</p>',
                'Email Configuration Test - If you receive this email, the configuration is working correctly!'
            );

            if ($testResult) {
                $results['success'] = true;
                $results['message'] = 'Email configuration test successful';
                $results['details']['test_email'] = 'Sent successfully';
            } else {
                $results['message'] = 'Email sending failed';
                $results['details']['test_email'] = 'Failed to send';
            }
        } else {
            $results['message'] = 'SMTP connection failed';
            $results['details']['smtp_connection'] = 'Failed';
        }

    } catch (Exception $e) {
        $results['message'] = 'Configuration test failed: ' . $e->getMessage();
        $results['details']['error'] = $e->getMessage();
    }

    return $results;
}

// Example usage (uncomment to test):
/*
// Test the configuration
$testResults = testEmailConfig();
if ($testResults['success']) {
    echo "Email configuration is working!";
} else {
    echo "Email configuration failed: " . $testResults['message'];
}
*/

?>