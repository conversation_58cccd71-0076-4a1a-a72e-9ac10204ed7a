<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Student - SSASIT Admin</title>
    <link rel="stylesheet" href="Css/Admin-Add_Data.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="form-header">
            <h1>Add New Student</h1>
            <p>Fill in the student details below</p>
        </div>

        <form class="student-form" id="studentForm" novalidate>
            <!-- Personal Information Section -->
            <div class="form-section">
                <h2 class="section-title">Personal Information</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="firstName">First Name *</label>
                        <input type="text" id="firstName" name="firstName" required 
                               pattern="[A-Za-z]{2,30}" 
                               title="First name should contain only letters (2-30 characters)">
                        <span class="error-message"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="lastName">Last Name *</label>
                        <input type="text" id="lastName" name="lastName" required 
                               pattern="[A-Za-z]{2,30}" 
                               title="Last name should contain only letters (2-30 characters)">
                        <span class="error-message"></span>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="dob">Date of Birth *</label>
                        <input type="date" id="dob" name="dob" required 
                               min="1990-01-01" max="2010-12-31">
                        <span class="error-message"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="gender">Gender *</label>
                        <select id="gender" name="gender" required>
                            <option value="">Select Gender</option>
                            <option value="male">Male</option>
                            <option value="female">Female</option>
                            <option value="other">Other</option>
                        </select>
                        <span class="error-message"></span>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group full-width">
                        <label for="address">Address *</label>
                        <textarea id="address" name="address" required 
                                  pattern=".{10,200}" 
                                  title="Address should be 10-200 characters long"
                                  placeholder="Enter complete address"></textarea>
                        <span class="error-message"></span>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="city">City *</label>
                        <select id="city" name="city" required>
                            <option value="">Select City</option>
                            <option value="mumbai">Mumbai</option>
                            <option value="delhi">Delhi</option>
                            <option value="bangalore">Bangalore</option>
                            <option value="hyderabad">Hyderabad</option>
                            <option value="ahmedabad">Ahmedabad</option>
                            <option value="chennai">Chennai</option>
                            <option value="kolkata">Kolkata</option>
                            <option value="pune">Pune</option>
                            <option value="jaipur">Jaipur</option>
                            <option value="lucknow">Lucknow</option>
                            <option value="kanpur">Kanpur</option>
                            <option value="nagpur">Nagpur</option>
                            <option value="indore">Indore</option>
                            <option value="thane">Thane</option>
                            <option value="bhopal">Bhopal</option>
                            <option value="visakhapatnam">Visakhapatnam</option>
                            <option value="pimpri">Pimpri-Chinchwad</option>
                            <option value="patna">Patna</option>
                            <option value="vadodara">Vadodara</option>
                            <option value="ghaziabad">Ghaziabad</option>
                        </select>
                        <span class="error-message"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="mobile">Mobile Number *</label>
                        <input type="tel" id="mobile" name="mobile" required 
                               pattern="[6-9][0-9]{9}" 
                               title="Mobile number should be 10 digits starting with 6-9"
                               placeholder="Enter 10-digit mobile number">
                        <span class="error-message"></span>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="email">Email Address *</label>
                        <input type="email" id="email" name="email" required 
                               pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$" 
                               title="Please enter a valid email address"
                               placeholder="<EMAIL>">
                        <span class="error-message"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="photo">Student Photo</label>
                        <input type="file" id="photo" name="photo" 
                               accept="image/jpeg,image/jpg,image/png" 
                               title="Please select a JPG or PNG image">
                        <span class="error-message"></span>
                    </div>
                </div>
            </div>

            <!-- Academic Information Section -->
            <div class="form-section">
                <h2 class="section-title">Academic Information</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="qualification">Qualification *</label>
                        <select id="qualification" name="qualification" required>
                            <option value="">Select Qualification</option>
                            <option value="10th">10th Pass</option>
                            <option value="12th">12th Pass</option>
                            <option value="diploma">Diploma</option>
                            <option value="graduate">Graduate</option>
                            <option value="postgraduate">Post Graduate</option>
                        </select>
                        <span class="error-message"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="department">Department *</label>
                        <select id="department" name="department" required>
                            <option value="">Select Department</option>
                            <option value="1">Computer Science Engineering</option>
                            <option value="2">Information Technology</option>
                            <option value="3">Electronics & Communication</option>
                            <option value="4">Mechanical Engineering</option>
                            <option value="5">Civil Engineering</option>
                            <option value="6">Electrical Engineering</option>
                        </select>
                        <span class="error-message"></span>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="admissionDate">Admission Date *</label>
                        <input type="date" id="admissionDate" name="admissionDate" required 
                               min="2020-01-01" max="2030-12-31">
                        <span class="error-message"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="status">Status *</label>
                        <select id="status" name="status" required>
                            <option value="">Select Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="graduated">Graduated</option>
                            <option value="suspended">Suspended</option>
                        </select>
                        <span class="error-message"></span>
                    </div>
                </div>
            </div>

            <!-- Form Buttons -->
            <div class="form-buttons">
                <button type="button" class="btn btn-secondary" onclick="resetForm()">Reset Form</button>
                <button type="submit" class="btn btn-primary">Add Student</button>
            </div>
        </form>
    </div>

    <script>
        // Simple form validation
        const form = document.getElementById('studentForm');
        const inputs = form.querySelectorAll('input, select, textarea');

        // Add event listeners for real-time validation
        inputs.forEach(input => {
            input.addEventListener('blur', validateField);
            input.addEventListener('input', clearError);
        });

        function validateField(e) {
            const field = e.target;
            const errorSpan = field.nextElementSibling;
            
            if (field.hasAttribute('required') && !field.value.trim()) {
                showError(field, errorSpan, 'This field is required');
                return false;
            }
            
            if (field.hasAttribute('pattern') && field.value && !field.checkValidity()) {
                showError(field, errorSpan, field.getAttribute('title'));
                return false;
            }
            
            if (field.type === 'email' && field.value && !field.checkValidity()) {
                showError(field, errorSpan, 'Please enter a valid email address');
                return false;
            }
            
            clearError(e);
            return true;
        }

        function showError(field, errorSpan, message) {
            field.classList.add('error');
            errorSpan.textContent = message;
            errorSpan.style.display = 'block';
        }

        function clearError(e) {
            const field = e.target;
            const errorSpan = field.nextElementSibling;
            field.classList.remove('error');
            errorSpan.style.display = 'none';
        }

        // Form submission
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            let isValid = true;
            inputs.forEach(input => {
                if (!validateField({target: input})) {
                    isValid = false;
                }
            });
            
            if (isValid) {
                alert('Form is valid! Ready to submit to database.');
                // Here you would normally send data to server
                console.log('Form data ready for submission');
            } else {
                alert('Please fix the errors in the form');
            }
        });

        // Reset form function
        function resetForm() {
            if (confirm('Are you sure you want to reset the form? All data will be lost.')) {
                form.reset();
                inputs.forEach(input => {
                    input.classList.remove('error');
                    const errorSpan = input.nextElementSibling;
                    if (errorSpan) errorSpan.style.display = 'none';
                });
            }
        }

        // Set today's date as default for admission date
        document.getElementById('admissionDate').valueAsDate = new Date();
    </script>
</body>
</html>
