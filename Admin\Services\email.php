<?php
function sendEmail($to, $subject, $message)
{
    try {
        require '../config/email_config.php';
        $mail->addAddress($to);
        $mail->isHTML(true);
        $mail->Subject= $subject;
        $mail->Body= $message;
        if ($mail->send()) {
            return true;
        } else {
            return false;
        }

    } catch (Exception $e) {
        error_log("Email sending failed: " . $e->getMessage());
        return false;
    }
}

sendEmail('<EMAIL>', 'Test Subject', 'Test Message');

?>