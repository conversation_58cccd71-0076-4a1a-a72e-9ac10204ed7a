<?php
// header("Content-Type: text/css"); 
require '../Auth/Authenticate.php';
require '../config/Session_config.php';
session_timeout();

// Handle logout
if (isset($_GET['logout'])) {
    session_start();
    session_unset();
    session_destroy();
    header('Location: ../Components/Admin-LogIn.php?logout=success');
    exit();
}

// Get admin info
$admin_username = isset($_SESSION['admin_username']) ? $_SESSION['admin_username'] : 'Admin';
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSASIT - Admin Dashboard</title>

    <link rel="stylesheet" href="../Css/Admin-Dashboard.css">
    <?php include '../config/Font-Awasome.php'; ?>
    <link rel="stylesheet" href="style.css?v=<?php echo time(); ?>">
</head>

<body>
    <?php include '../Components/Admin-Header.php'; ?>
    <div class="dashboard-container">
        <?php include '../Components/Admin-Welcome.php'; ?>

        <?php include '../Components/Admin-Card.php'; ?>
    </div>

    <script>
        // Card Action Functions
 



        // Add loading animation to cards on hover


        // Add click animation to logout button


        // Add welcome animation on page load
        document.addEventListener('DOMContentLoaded', function () {
            const welcomeSection = document.querySelector('.welcome-section');
            const cards = document.querySelectorAll('.dashboard-card');

            // Animate welcome section
            welcomeSection.style.opacity = '0';
            welcomeSection.style.transform = 'translateY(20px)';

            setTimeout(() => {
                welcomeSection.style.transition = 'all 0.6s ease';
                welcomeSection.style.opacity = '1';
                welcomeSection.style.transform = 'translateY(0)';
            }, 100);

            // Animate cards with stagger effect
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 200 + (index * 100));
            });
        });

        // Add touch support for mobile devices
        if ('ontouchstart' in window) {
            dashboardCards.forEach(card => {
                card.addEventListener('touchstart', function () {
                    this.style.transform = 'translateY(-3px) scale(1.01)';
                });

                card.addEventListener('touchend', function () {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        }

        // Prevent back button after logout
        window.addEventListener('popstate', function (e) {
            if (window.location.search.includes('logout=success')) {
                window.location.replace('../Components/Admin-LogIn.php');
            }
        });

        // Security: Clear sensitive data on page unload
        window.addEventListener('beforeunload', function () {
            // Clear any sensitive data if needed
            sessionStorage.clear();
        });

        // Add visual feedback for successful operations
        function showSuccessMessage(message) {
            const successDiv = document.createElement('div');
            successDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--success-color);
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 0.6rem;
                font-family: var(--primary-font);
                font-weight: 600;
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;
            successDiv.textContent = message;
            document.body.appendChild(successDiv);

            setTimeout(() => {
                successDiv.remove();
            }, 3000);
        }

        // Add CSS animation for success message
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    opacity: 0;
                    transform: translateX(100%);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>

</html>