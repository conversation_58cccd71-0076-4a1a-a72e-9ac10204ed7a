<?php
require '../auth/Authenticate.php';
require '../config/Session_config.php';
session_timeout();

// Handle logout
if (isset($_GET['logout'])) {
    session_start();
    session_unset();
    session_destroy();
    header('Location: ../Components/Admin-LogIn.php?logout=success');
    exit();
}

// Get admin info
$admin_username = isset($_SESSION['admin_username']) ? $_SESSION['admin_username'] : 'Admin';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSASIT - Admin Dashboard</title>
    <link rel="stylesheet" href="../index.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <?php include '../config/Font-Awasome.php'; ?>
    <link rel="stylesheet" href="../Css/Admin-Dashboard.css">
</head>
<body>
    <?php include '../Components/Admin-Header.php'; ?>
    <div class="dashboard-container">
        <?php include '../Components/Admin-Welcome.php'; ?>

        <div class="dashboard-grid">
        <div class="dashboard-card">
            <div class="card-header">

                <div class="card-icon"><i class="fa-light fa-user-graduate fa-sm"></i></div>
                <h3 class="card-title">total student</h3>
            </div>
            <div class="card-content">
                <p>The dashboard provides insights into the total number of students and student related all operation for admin</p>
                <div class="card-stats">
                    <div class="stat-item">
                        <div class="stat-value">0</div>
                        <div class="stat-label">Total Student</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">1</div>
                        <div class="stat-label">Active</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">1</div>
                        <div class="stat-label">Graduate</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">1</div>
                        <div class="stat-label">Terminated</div>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="action-btn view-btn" onclick="viewAllStudents()">
                        <i class="fa-regular fa-eye"></i>
                        View All Students
                    </button>
                    <button class="action-btn add-btn" onclick="addNewStudent()">
                        <i class="fa-regular fa-plus"></i>
                        Add New Student
                    </button>
                </div>
            </div>
        </div>

        <div class="dashboard-card">
            <div class="card-header">
                <div class="card-icon"><i class="fa-light fa-chalkboard-user fa-sm"></i></div>
                <h3 class="card-title">total facultys</h3>
            </div>
            <div class="card-content">
                <p>Manage faculty members, their profiles, assignments, and academic responsibilities across all departments.</p>
                <div class="card-stats">
                    <div class="stat-item">
                        <div class="stat-value">45</div>
                        <div class="stat-label">Total Faculty</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">42</div>
                        <div class="stat-label">Active Faculty</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">15</div>
                        <div class="stat-label">Professors</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">30</div>
                        <div class="stat-label">Assistant Prof.</div>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="action-btn view-btn" onclick="viewAllFaculty()">
                        <i class="fa-regular fa-eye"></i>
                        View All Faculty
                    </button>
                    <button class="action-btn add-btn" onclick="addNewFaculty()">
                        <i class="fa-regular fa-plus"></i>
                        Add New Faculty
                    </button>
                </div>
            </div>
        </div>

        <div class="dashboard-card">
            <div class="card-header">
                <div class="card-icon"><i class="fa-regular fa-buildings"></i></div>
                <h3 class="card-title">Total Departments</h3>
            </div>
            <div class="card-content">
                <p>The dashboard provides insights into the total number of Departments and Departments related all operation for admin</p>
                <div class="card-stats">
                    <div class="stat-item">
                        <div class="stat-value">6</div>
                        <div class="stat-label">Total Departments</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">5</div>
                        <div class="stat-label">Active Departments</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">5</div>
                        <div class="stat-label">Total HOD's</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">1</div>
                        <div class="stat-label">Active buildings</div>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="action-btn view-btn" onclick="viewAllDepartments()">
                        <i class="fa-regular fa-eye"></i>
                        View All Departments
                    </button>
                    <button class="action-btn add-btn" onclick="addNewDepartment()">
                        <i class="fa-regular fa-plus"></i>
                        Add New Department
                    </button>
                </div>
            </div>
        </div>

        

        <div class="dashboard-card">
            <div class="card-header">
                <div class="card-icon">🔒</div>
                <h3 class="card-title">overall result</h3>
            </div>
            <div class="card-content">
                <p>View comprehensive academic results, performance analytics, and student achievement statistics across all programs.</p>
                <div class="card-stats">
                    <div class="stat-item">
                        <div class="stat-value">85%</div>
                        <div class="stat-label">Pass Rate</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">7.2</div>
                        <div class="stat-label">Avg CGPA</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">92%</div>
                        <div class="stat-label">Attendance</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">15</div>
                        <div class="stat-label">Toppers</div>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="action-btn view-btn" onclick="viewAllResults()">
                        <i class="fa-regular fa-eye"></i>
                        View All Results
                    </button>
                    <button class="action-btn add-btn" onclick="addNewResult()">
                        <i class="fa-regular fa-plus"></i>
                        Add New Result
                    </button>
                </div>
            </div>
        </div>

        <div class="dashboard-card">
            <div class="card-header">
                <div class="card-icon"><i class="fa-regular fa-newspaper"></i></div>
                <h3 class="card-title">total recent news</h3>
            </div>
            <div class="card-content">
                <p>Stay updated with latest institutional news, announcements, events, and important notifications for students and faculty.</p>
                <div class="card-stats">
                    <div class="stat-item">
                        <div class="stat-value">12</div>
                        <div class="stat-label">Recent News</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">5</div>
                        <div class="stat-label">Announcements</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">8</div>
                        <div class="stat-label">Events</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">3</div>
                        <div class="stat-label">Urgent Notices</div>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="action-btn view-btn" onclick="viewAllNews()">
                        <i class="fa-regular fa-eye"></i>
                        View All News
                    </button>
                    <button class="action-btn add-btn" onclick="addNewNews()">
                        <i class="fa-regular fa-plus"></i>
                        Add New News
                    </button>
                </div>
            </div>
        </div>

        <div class="dashboard-card">
            <div class="card-header">
                <div class="card-icon"><i class="fa-regular fa-file-lines"></i></div>
                <h3 class="card-title">exam related</h3>
            </div>
            <div class="card-content">
                <p>Manage examination schedules, results, hall tickets, and all exam-related administrative tasks and student queries.</p>
                <div class="card-stats">
                    <div class="stat-item">
                        <div class="stat-value">6</div>
                        <div class="stat-label">Upcoming Exams</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">4</div>
                        <div class="stat-label">Results Pending</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">150</div>
                        <div class="stat-label">Hall Tickets</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">2</div>
                        <div class="stat-label">Exam Halls</div>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="action-btn view-btn" onclick="viewAllExams()">
                        <i class="fa-regular fa-eye"></i>
                        View All Exams
                    </button>
                    <button class="action-btn add-btn" onclick="addNewExam()">
                        <i class="fa-regular fa-plus"></i>
                        Add New Exam
                    </button>
                </div>
            </div>
        </div>
    </div>
    </div>

    <script>
        // Card Action Functions
        // Student Functions
        function viewAllStudents() {
            showSuccessMessage('View All Students functionality will be implemented soon!');
            console.log('View All Students clicked');
        }

        function addNewStudent() {
            showSuccessMessage('Add New Student functionality will be implemented soon!');
            console.log('Add New Student clicked');
        }

        // Faculty Functions
        function viewAllFaculty() {
            showSuccessMessage('View All Faculty functionality will be implemented soon!');
            console.log('View All Faculty clicked');
        }

        function addNewFaculty() {
            showSuccessMessage('Add New Faculty functionality will be implemented soon!');
            console.log('Add New Faculty clicked');
        }

        // Department Functions
        function viewAllDepartments() {
            showSuccessMessage('View All Departments functionality will be implemented soon!');
            console.log('View All Departments clicked');
        }

        function addNewDepartment() {
            showSuccessMessage('Add New Department functionality will be implemented soon!');
            console.log('Add New Department clicked');
        }

        // Result Functions
        function viewAllResults() {
            showSuccessMessage('View All Results functionality will be implemented soon!');
            console.log('View All Results clicked');
        }

        function addNewResult() {
            showSuccessMessage('Add New Result functionality will be implemented soon!');
            console.log('Add New Result clicked');
        }

        // News Functions
        function viewAllNews() {
            showSuccessMessage('View All News functionality will be implemented soon!');
            console.log('View All News clicked');
        }

        function addNewNews() {
            showSuccessMessage('Add New News functionality will be implemented soon!');
            console.log('Add New News clicked');
        }

        // Exam Functions
        function viewAllExams() {
            showSuccessMessage('View All Exams functionality will be implemented soon!');
            console.log('View All Exams clicked');
        }

        function addNewExam() {
            showSuccessMessage('Add New Exam functionality will be implemented soon!');
            console.log('Add New Exam clicked');
        }

        window.addEventListener('scroll', function() {
            const header = document.querySelector('.dashboard-header');
            header.style.transition = 'var(--transition)';
            if (window.scrollY > 25) {
                // header.style.boxShadow = '0 4px 10px rgba(3, 108, 226, 0.3)';
                header.style.backgroundColor = 'var(--white)';
                header.style.height = '70px';
            }
            else if(window.scrollY < 30){
                header.style.backgroundColor = 'transparent';
                header.style.height = '80px';
            }
        });
        // Add loading animation to cards on hover
        // Logout confirmation function
        function confirmLogout() {
            return confirm('Are you sure you want to logout? You will be redirected to the login page.');
        }


        // Add loading animation to cards on hover
        const dashboardCards = document.querySelectorAll('.dashboard-card');
        dashboardCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Add click animation to logout button
        const logoutBtn = document.querySelector('.logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('mousedown', function() {
                this.style.transform = 'scale(0.98)';
            });

            logoutBtn.addEventListener('mouseup', function() {
                this.style.transform = 'scale(1.02)';
            });

            logoutBtn.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        }

        // Add welcome animation on page load
        document.addEventListener('DOMContentLoaded', function() {
            const welcomeSection = document.querySelector('.welcome-section');
            const cards = document.querySelectorAll('.dashboard-card');

            // Animate welcome section
            welcomeSection.style.opacity = '0';
            welcomeSection.style.transform = 'translateY(20px)';

            setTimeout(() => {
                welcomeSection.style.transition = 'all 0.6s ease';
                welcomeSection.style.opacity = '1';
                welcomeSection.style.transform = 'translateY(0)';
            }, 100);

            // Animate cards with stagger effect
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 200 + (index * 100));
            });
        });

        // Add touch support for mobile devices
        if ('ontouchstart' in window) {
            dashboardCards.forEach(card => {
                card.addEventListener('touchstart', function() {
                    this.style.transform = 'translateY(-3px) scale(1.01)';
                });

                card.addEventListener('touchend', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        }

        // Prevent back button after logout
        window.addEventListener('popstate', function(e) {
            if (window.location.search.includes('logout=success')) {
                window.location.replace('../Components/Admin-LogIn.php');
            }
        });

        // Security: Clear sensitive data on page unload
        window.addEventListener('beforeunload', function() {
            // Clear any sensitive data if needed
            sessionStorage.clear();
        });

        // Add visual feedback for successful operations
        function showSuccessMessage(message) {
            const successDiv = document.createElement('div');
            successDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--success-color);
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 0.6rem;
                font-family: var(--primary-font);
                font-weight: 600;
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;
            successDiv.textContent = message;
            document.body.appendChild(successDiv);

            setTimeout(() => {
                successDiv.remove();
            }, 3000);
        }

        // Add CSS animation for success message
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    opacity: 0;
                    transform: translateX(100%);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>