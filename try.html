<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Palette Navbar</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8fafc;
        }
        
        .dropdown {
            position: relative;
            display: inline-block;
        }
        
        .dropdown-content {
            visibility: hidden;
            opacity: 0;
            position: absolute;
            min-width: 250px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            z-index: 1;
            border-radius: 12px;
            transform: translateY(10px);
            transition: all 0.3s ease;
        }
        
        .dropdown:hover .dropdown-content {
            visibility: visible;
            opacity: 1;
            transform: translateY(0);
        }
        
        .nav-link {
            position: relative;
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -4px;
            left: 0;
            background-color: #6366f1;
            transition: width 0.3s ease;
        }
        
        .nav-link:hover::after {
            width: 100%;
        }
        
        .color-palette {
            display: flex;
            margin-right: 12px;
        }
        
        .color-dot {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            margin-right: 4px;
        }
        
        .mobile-menu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.5s ease;
        }
        
        .mobile-menu.active {
            max-height: 1000px;
        }
        
        .mobile-dropdown-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            padding-left: 1rem;
        }
        
        .mobile-dropdown.active .mobile-dropdown-content {
            max-height: 500px;
        }
        
        .mobile-dropdown-icon {
            transition: transform 0.3s ease;
        }
        
        .mobile-dropdown.active .mobile-dropdown-icon {
            transform: rotate(180deg);
        }
    </style>
</head>
<body>
    <!-- Main Navigation -->
    <nav class="bg-white shadow-md sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <!-- Logo -->
                <div class="flex items-center space-x-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10" fill="#6366f1" stroke="none"/>
                        <path d="M8 12a4 4 0 0 1 8 0" fill="#8b5cf6" stroke="none"/>
                        <circle cx="12" cy="12" r="3" fill="#ec4899" stroke="none"/>
                    </svg>
                    <span class="font-bold text-xl text-gray-800">ColorHub</span>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#" class="nav-link text-gray-700 hover:text-indigo-600 font-medium transition duration-300">Home</a>
                    
                    <!-- Color Palettes Dropdown -->
                    <div class="dropdown">
                        <a href="#" class="nav-link text-gray-700 hover:text-indigo-600 font-medium transition duration-300 flex items-center">
                            Color Palettes
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </a>
                        <div class="dropdown-content bg-white p-4 mt-4">
                            <a href="#" class="block py-3 px-4 hover:bg-gray-50 rounded-lg transition duration-200 mb-1 flex items-center">
                                <div class="color-palette">
                                    <span class="color-dot" style="background-color: #f472b6;"></span>
                                    <span class="color-dot" style="background-color: #a78bfa;"></span>
                                    <span class="color-dot" style="background-color: #60a5fa;"></span>
                                </div>
                                <span>Pastel Dreams</span>
                            </a>
                            <a href="#" class="block py-3 px-4 hover:bg-gray-50 rounded-lg transition duration-200 mb-1 flex items-center">
                                <div class="color-palette">
                                    <span class="color-dot" style="background-color: #10b981;"></span>
                                    <span class="color-dot" style="background-color: #34d399;"></span>
                                    <span class="color-dot" style="background-color: #6ee7b7;"></span>
                                </div>
                                <span>Nature Greens</span>
                            </a>
                            <a href="#" class="block py-3 px-4 hover:bg-gray-50 rounded-lg transition duration-200 mb-1 flex items-center">
                                <div class="color-palette">
                                    <span class="color-dot" style="background-color: #f97316;"></span>
                                    <span class="color-dot" style="background-color: #fb923c;"></span>
                                    <span class="color-dot" style="background-color: #fdba74;"></span>
                                </div>
                                <span>Sunset Orange</span>
                            </a>
                            <a href="#" class="block py-3 px-4 hover:bg-gray-50 rounded-lg transition duration-200 flex items-center">
                                <div class="color-palette">
                                    <span class="color-dot" style="background-color: #0ea5e9;"></span>
                                    <span class="color-dot" style="background-color: #38bdf8;"></span>
                                    <span class="color-dot" style="background-color: #7dd3fc;"></span>
                                </div>
                                <span>Ocean Blue</span>
                            </a>
                            <div class="mt-3 pt-3 border-t border-gray-100">
                                <a href="#" class="block py-2 px-4 text-indigo-600 hover:bg-indigo-50 rounded-lg transition duration-200 font-medium">
                                    View All Palettes →
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Trending Dropdown -->
                    <div class="dropdown">
                        <a href="#" class="nav-link text-gray-700 hover:text-indigo-600 font-medium transition duration-300 flex items-center">
                            Trending
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </a>
                        <div class="dropdown-content bg-white p-4 mt-4">
                            <a href="#" class="block py-3 px-4 hover:bg-gray-50 rounded-lg transition duration-200 mb-1 flex items-center">
                                <div class="color-palette">
                                    <span class="color-dot" style="background-color: #8b5cf6;"></span>
                                    <span class="color-dot" style="background-color: #a78bfa;"></span>
                                    <span class="color-dot" style="background-color: #c4b5fd;"></span>
                                </div>
                                <span>2024 Color of the Year</span>
                            </a>
                            <a href="#" class="block py-3 px-4 hover:bg-gray-50 rounded-lg transition duration-200 mb-1 flex items-center">
                                <div class="color-palette">
                                    <span class="color-dot" style="background-color: #ec4899;"></span>
                                    <span class="color-dot" style="background-color: #f472b6;"></span>
                                    <span class="color-dot" style="background-color: #f9a8d4;"></span>
                                </div>
                                <span>Most Popular</span>
                            </a>
                            <a href="#" class="block py-3 px-4 hover:bg-gray-50 rounded-lg transition duration-200 mb-1 flex items-center">
                                <div class="color-palette">
                                    <span class="color-dot" style="background-color: #0d9488;"></span>
                                    <span class="color-dot" style="background-color: #14b8a6;"></span>
                                    <span class="color-dot" style="background-color: #5eead4;"></span>
                                </div>
                                <span>Designer Picks</span>
                            </a>
                            <div class="mt-3 pt-3 border-t border-gray-100">
                                <a href="#" class="block py-2 px-4 text-indigo-600 hover:bg-indigo-50 rounded-lg transition duration-200 font-medium">
                                    See All Trends →
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tools Dropdown -->
                    <div class="dropdown">
                        <a href="#" class="nav-link text-gray-700 hover:text-indigo-600 font-medium transition duration-300 flex items-center">
                            Tools
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </a>
                        <div class="dropdown-content bg-white p-4 mt-4">
                            <a href="#" class="block py-3 px-4 hover:bg-gray-50 rounded-lg transition duration-200 mb-1">
                                <span class="font-medium">Color Picker</span>
                                <p class="text-sm text-gray-500 mt-1">Extract colors from images</p>
                            </a>
                            <a href="#" class="block py-3 px-4 hover:bg-gray-50 rounded-lg transition duration-200 mb-1">
                                <span class="font-medium">Palette Generator</span>
                                <p class="text-sm text-gray-500 mt-1">Create harmonious color schemes</p>
                            </a>
                            <a href="#" class="block py-3 px-4 hover:bg-gray-50 rounded-lg transition duration-200 mb-1">
                                <span class="font-medium">Contrast Checker</span>
                                <p class="text-sm text-gray-500 mt-1">Test accessibility compliance</p>
                            </a>
                            <div class="mt-3 pt-3 border-t border-gray-100">
                                <a href="#" class="block py-2 px-4 text-indigo-600 hover:bg-indigo-50 rounded-lg transition duration-200 font-medium">
                                    View All Tools →
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <a href="#" class="nav-link text-gray-700 hover:text-indigo-600 font-medium transition duration-300">Inspiration</a>
                    <a href="#" class="nav-link text-gray-700 hover:text-indigo-600 font-medium transition duration-300">Blog</a>
                </div>
                
                <!-- Search and Mobile Menu Button -->
                <div class="flex items-center space-x-4">
                    <button class="hidden md:block text-gray-700 hover:text-indigo-600 transition duration-300">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </button>
                    <button id="mobile-menu-button" class="md:hidden text-gray-700 hover:text-indigo-600 focus:outline-none">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- Mobile Menu -->
            <div id="mobile-menu" class="mobile-menu md:hidden pb-4">
                <a href="#" class="block py-3 text-gray-700 hover:text-indigo-600 border-b border-gray-100">Home</a>
                
                <!-- Mobile Color Palettes Dropdown -->
                <div class="mobile-dropdown border-b border-gray-100">
                    <button class="mobile-dropdown-button flex items-center justify-between w-full text-left py-3 text-gray-700 hover:text-indigo-600">
                        Color Palettes
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mobile-dropdown-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div class="mobile-dropdown-content py-2">
                        <a href="#" class="block py-2 text-gray-700 hover:text-indigo-600 flex items-center">
                            <div class="color-palette">
                                <span class="color-dot" style="background-color: #f472b6;"></span>
                                <span class="color-dot" style="background-color: #a78bfa;"></span>
                                <span class="color-dot" style="background-color: #60a5fa;"></span>
                            </div>
                            <span>Pastel Dreams</span>
                        </a>
                        <a href="#" class="block py-2 text-gray-700 hover:text-indigo-600 flex items-center">
                            <div class="color-palette">
                                <span class="color-dot" style="background-color: #10b981;"></span>
                                <span class="color-dot" style="background-color: #34d399;"></span>
                                <span class="color-dot" style="background-color: #6ee7b7;"></span>
                            </div>
                            <span>Nature Greens</span>
                        </a>
                        <a href="#" class="block py-2 text-gray-700 hover:text-indigo-600 flex items-center">
                            <div class="color-palette">
                                <span class="color-dot" style="background-color: #f97316;"></span>
                                <span class="color-dot" style="background-color: #fb923c;"></span>
                                <span class="color-dot" style="background-color: #fdba74;"></span>
                            </div>
                            <span>Sunset Orange</span>
                        </a>
                        <a href="#" class="block py-2 text-gray-700 hover:text-indigo-600 flex items-center">
                            <div class="color-palette">
                                <span class="color-dot" style="background-color: #0ea5e9;"></span>
                                <span class="color-dot" style="background-color: #38bdf8;"></span>
                                <span class="color-dot" style="background-color: #7dd3fc;"></span>
                            </div>
                            <span>Ocean Blue</span>
                        </a>
                        <a href="#" class="block py-2 text-indigo-600 hover:text-indigo-800 font-medium">
                            View All Palettes →
                        </a>
                    </div>
                </div>
                
                <!-- Mobile Trending Dropdown -->
                <div class="mobile-dropdown border-b border-gray-100">
                    <button class="mobile-dropdown-button flex items-center justify-between w-full text-left py-3 text-gray-700 hover:text-indigo-600">
                        Trending
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mobile-dropdown-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div class="mobile-dropdown-content py-2">
                        <a href="#" class="block py-2 text-gray-700 hover:text-indigo-600 flex items-center">
                            <div class="color-palette">
                                <span class="color-dot" style="background-color: #8b5cf6;"></span>
                                <span class="color-dot" style="background-color: #a78bfa;"></span>
                                <span class="color-dot" style="background-color: #c4b5fd;"></span>
                            </div>
                            <span>2024 Color of the Year</span>
                        </a>
                        <a href="#" class="block py-2 text-gray-700 hover:text-indigo-600 flex items-center">
                            <div class="color-palette">
                                <span class="color-dot" style="background-color: #ec4899;"></span>
                                <span class="color-dot" style="background-color: #f472b6;"></span>
                                <span class="color-dot" style="background-color: #f9a8d4;"></span>
                            </div>
                            <span>Most Popular</span>
                        </a>
                        <a href="#" class="block py-2 text-gray-700 hover:text-indigo-600 flex items-center">
                            <div class="color-palette">
                                <span class="color-dot" style="background-color: #0d9488;"></span>
                                <span class="color-dot" style="background-color: #14b8a6;"></span>
                                <span class="color-dot" style="background-color: #5eead4;"></span>
                            </div>
                            <span>Designer Picks</span>
                        </a>
                        <a href="#" class="block py-2 text-indigo-600 hover:text-indigo-800 font-medium">
                            See All Trends →
                        </a>
                    </div>
                </div>
                
                <!-- Mobile Tools Dropdown -->
                <div class="mobile-dropdown border-b border-gray-100">
                    <button class="mobile-dropdown-button flex items-center justify-between w-full text-left py-3 text-gray-700 hover:text-indigo-600">
                        Tools
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mobile-dropdown-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div class="mobile-dropdown-content py-2">
                        <a href="#" class="block py-2 text-gray-700 hover:text-indigo-600">
                            <span class="font-medium">Color Picker</span>
                            <p class="text-sm text-gray-500">Extract colors from images</p>
                        </a>
                        <a href="#" class="block py-2 text-gray-700 hover:text-indigo-600">
                            <span class="font-medium">Palette Generator</span>
                            <p class="text-sm text-gray-500">Create harmonious color schemes</p>
                        </a>
                        <a href="#" class="block py-2 text-gray-700 hover:text-indigo-600">
                            <span class="font-medium">Contrast Checker</span>
                            <p class="text-sm text-gray-500">Test accessibility compliance</p>
                        </a>
                        <a href="#" class="block py-2 text-indigo-600 hover:text-indigo-800 font-medium">
                            View All Tools →
                        </a>
                    </div>
                </div>
                
                <a href="#" class="block py-3 text-gray-700 hover:text-indigo-600 border-b border-gray-100">Inspiration</a>
                <a href="#" class="block py-3 text-gray-700 hover:text-indigo-600 border-b border-gray-100">Blog</a>
            </div>
        </div>
    </nav>
    
    <!-- Hero Section -->
    <div class="container mx-auto px-4 py-12">
        <div class="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl p-8 text-white text-center shadow-lg">
            <h1 class="text-3xl md:text-4xl font-bold mb-4">Discover Beautiful Color Palettes</h1>
            <p class="text-lg mb-6 max-w-2xl mx-auto">Find the perfect color combination for your next design project with our curated collection of trending palettes</p>
            <button class="bg-white text-indigo-600 font-medium px-6 py-3 rounded-lg hover:bg-gray-100 transition duration-300 shadow-md">
                Explore Collections
            </button>
        </div>
        
        <!-- Featured Palettes Preview -->
        <div class="mt-12">
            <h2 class="text-2xl font-bold text-gray-800 mb-6">Featured Color Palettes</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Palette 1 -->
                <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition duration-300">
                    <div class="h-24 flex">
                        <div class="w-1/4 bg-[#f472b6]"></div>
                        <div class="w-1/4 bg-[#a78bfa]"></div>
                        <div class="w-1/4 bg-[#60a5fa]"></div>
                        <div class="w-1/4 bg-[#34d399]"></div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-medium text-gray-800">Pastel Dreams</h3>
                        <p class="text-sm text-gray-500 mt-1">Soft and dreamy colors</p>
                    </div>
                </div>
                
                <!-- Palette 2 -->
                <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition duration-300">
                    <div class="h-24 flex">
                        <div class="w-1/4 bg-[#0d9488]"></div>
                        <div class="w-1/4 bg-[#14b8a6]"></div>
                        <div class="w-1/4 bg-[#2dd4bf]"></div>
                        <div class="w-1/4 bg-[#5eead4]"></div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-medium text-gray-800">Teal Harmony</h3>
                        <p class="text-sm text-gray-500 mt-1">Fresh and calming tones</p>
                    </div>
                </div>
                
                <!-- Palette 3 -->
                <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition duration-300">
                    <div class="h-24 flex">
                        <div class="w-1/4 bg-[#f97316]"></div>
                        <div class="w-1/4 bg-[#fb923c]"></div>
                        <div class="w-1/4 bg-[#fdba74]"></div>
                        <div class="w-1/4 bg-[#fed7aa]"></div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-medium text-gray-800">Sunset Orange</h3>
                        <p class="text-sm text-gray-500 mt-1">Warm and energetic vibes</p>
                    </div>
                </div>
                
                <!-- Palette 4 -->
                <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition duration-300">
                    <div class="h-24 flex">
                        <div class="w-1/4 bg-[#8b5cf6]"></div>
                        <div class="w-1/4 bg-[#a78bfa]"></div>
                        <div class="w-1/4 bg-[#c4b5fd]"></div>
                        <div class="w-1/4 bg-[#ddd6fe]"></div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-medium text-gray-800">Purple Haze</h3>
                        <p class="text-sm text-gray-500 mt-1">Trendy violet gradients</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('active');
        });
        
        // Mobile dropdown toggles
        const mobileDropdownButtons = document.querySelectorAll('.mobile-dropdown-button');
        
        mobileDropdownButtons.forEach(button => {
            button.addEventListener('click', () => {
                const parent = button.parentElement;
                parent.classList.toggle('active');
            });
        });
    </script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'9620895594603bb0',t:'MTc1Mjk5NDM1My4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>
</html>
